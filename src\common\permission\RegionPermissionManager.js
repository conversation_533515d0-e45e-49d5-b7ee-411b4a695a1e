import BasePermissionManager from './BasePermissionManager.js';

/**
 * 区域权限管理器
 * 管理基于区域配置的功能权限（functionsStatus）
 * 这些权限在应用初始化时获取，不会因为退出登录而变化
 * 已与store解耦，通过外部调用updateRegionFunctions方法更新数据
 */
class RegionPermissionManager extends BasePermissionManager {
    constructor() {
        super();
        this.regionFunctions = {}; // 区域功能配置
        this.region = ''; // 当前区域
    }

    /**
     * 初始化区域权限管理器
     * @param {Object} userInfo - 用户信息
     * @param {Object} config - 配置信息
     */
    async initialize(userInfo, config = {}) {
        this.userInfo = userInfo;
        this.config = config;
        this.initialized = true;
        await this.loadPermissions();
    }

    /**
     * 加载权限数据（实现基类的抽象方法）
     * 现在只加载默认配置，实际数据通过updateRegionFunctions方法更新
     */
    async loadPermissions() {
        try {
            // 使用默认配置作为初始值
            this.regionFunctions = this.getDefaultRegionFunctions();
            this.region = '';

            console.log('RegionPermissionManager loaded with default functions:', {
                region: this.region,
                functions: this.regionFunctions,
                isDefault: true
            });
        } catch (error) {
            console.error('Failed to load region permissions:', error);
            // 使用默认配置
            this.regionFunctions = this.getDefaultRegionFunctions();
        }
    }

    /**
     * 更新区域功能配置（外部调用）
     * @param {Object} functionsStatus - 功能状态配置
     * @param {string} region - 区域信息
     */
    updateRegionFunctions(functionsStatus, region = '') {
        if (!functionsStatus || typeof functionsStatus !== 'object') {
            console.warn('RegionPermissionManager: Invalid functionsStatus provided');
            return;
        }

        const oldFunctions = { ...this.regionFunctions };
        const oldRegion = this.region;

        // 更新区域功能配置
        this.regionFunctions = { ...functionsStatus };
        this.region = region;

        // 清除缓存以确保权限检查使用新数据
        this.clearCache();

        console.log('RegionPermissionManager: functionsStatus updated externally', {
            oldFunctions,
            newFunctions: this.regionFunctions,
            oldRegion,
            newRegion: this.region
        });

        // 触发权限变化事件（如果有变化）
        if (JSON.stringify(oldFunctions) !== JSON.stringify(this.regionFunctions) || oldRegion !== this.region) {
            this.emitRegionPermissionChange({
                type: 'regionFunctions',
                oldFunctions,
                newFunctions: this.regionFunctions,
                oldRegion,
                newRegion: this.region
            });
        }
    }

    /**
     * 触发区域权限变化事件
     * @param {Object} changeInfo - 变化信息
     */
    emitRegionPermissionChange(changeInfo) {
        // 触发浏览器事件
        if (typeof window !== 'undefined') {
            const event = new CustomEvent('permission:regionChanged', {
                detail: changeInfo
            });
            window.dispatchEvent(event);
        }

        // 记录日志
        console.log('[RegionPermissionManager] Region permission changed:', changeInfo);
    }

    /**
     * 获取区域功能权限映射关系
     * 定义区域功能与具体权限操作的映射关系
     * @returns {Object} 权限映射配置
     */
    getRegionPermissionMapping() {
        return {
            // 直播功能映射
            live: [
                'liveStart',           // 开始直播
                'liveJoin',            // 加入直播
                'liveManagement',      // 直播管理
                'liveInvite',          // 直播邀请
                'liveRecord',          // 直播录制
            ],

            // 电视墙功能映射
            tvwall: [
                'tvWallPlay',          // 电视墙播放
                'tvWallPush',          // 电视墙推送
                'tvWallManage',        // 电视墙管理
                'tvWallControl',       // 电视墙控制
                'webTvwallEnterConversation', // web电视墙进入会话
            ],

            // 多中心功能映射
            multicenter: [
                'multicenterCreate',   // 多中心创建
                'multicenterJoin',     // 多中心加入
                'multicenterManage',   // 多中心管理
                'multicenterReview',   // 多中心审核
            ],

            // AI功能映射
            ai: [
                'aiAnalysis',          // AI分析
                'aiReport',            // AI报告
                'aiChat',              // AI聊天
            ],

            // 文件管理功能映射
            fileManage: [
                'fileUpload',          // 文件上传
                'fileDownload',        // 文件下载
                'fileDelete',          // 文件删除
                'fileShare',           // 文件分享
            ]
        };
    }

    /**
     * 获取默认区域功能配置
     * @returns {Object} 默认功能配置
     */
    getDefaultRegionFunctions() {
        return {
            live:1, //直播
            library:1, //图书馆
            cloudStatistic:1, //云端统计
            breastCases:1, //乳腺病例库
            breastAI:1, //小麦同学
            drAIAssistant:1, //dr助手
            groupset:1, //群落
            wechat:1, //微信功能
            obstetricalAI:1, //产科AI
            tvwall:1, //电视墙
            qcStatistics:1, //bi统计
            referralCode: 1,//推荐码
            webShareScreen:1,//web推桌面
            webTvwallEnterConversation:1,//web电视墙进入会话
            ai:1, //AI应用
            smartEdTechTraining:1, //智能教培
            ultrasoundQCReport:1, //超声质控报告
            club:1, //Mindray Club
            professionalIdentityForce:0,//职业身份强制设置
        };
    }

    /**
     * 检查区域功能是否启用
     * @param {string} functionName - 功能名称
     * @returns {boolean} 是否启用
     */
    isRegionFunctionEnabled(functionName) {
        return !!this.regionFunctions[functionName];
    }

    /**
     * 检查区域功能权限
     * @param {string} functionName - 功能名称
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    hasPermission(functionName, context = {}) {
        if (!this.isInitialized()) {
            console.warn('RegionPermissionManager not initialized');
            return false;
        }

        // 使用缓存机制
        return this.checkWithCache('region', functionName, context, () => {
            return this.isRegionFunctionEnabled(functionName);
        });
    }

    /**
     * 检查区域功能是否可用（启用且有权限）
     * @param {string} functionName - 功能名称
     * @param {Object} options - 选项
     * @returns {boolean} 是否可用
     */
    isRegionFunctionAvailable(functionName, options = {}) {
        // 使用缓存机制
        return this.checkWithCache('region_available', functionName, options, () => {
            // 检查功能是否启用
            if (!this.isRegionFunctionEnabled(functionName)) {
                return false;
            }

            // 如果需要额外的权限检查，可以在这里添加
            if (options.requireUserPermission && this.userInfo) {
                // 可以添加基于用户角色的额外检查
                return true;
            }

            return true;
        });
    }

    /**
     * 获取启用的区域功能列表
     * @returns {Array<string>} 启用的功能列表
     */
    getEnabledRegionFunctions() {
        return Object.keys(this.regionFunctions).filter(func => this.regionFunctions[func]);
    }

    /**
     * 获取当前区域
     * @returns {string} 当前区域
     */
    getCurrentRegion() {
        return this.region;
    }

    /**
     * 获取区域功能映射的权限列表
     * @param {string} regionFunction - 区域功能名称
     * @returns {Array<string>} 映射的权限列表
     */
    getRegionMappedPermissions(regionFunction) {
        const mapping = this.getRegionPermissionMapping();
        return mapping[regionFunction] || [];
    }

    /**
     * 检查区域功能映射的所有权限
     * @param {string} regionFunction - 区域功能名称
     * @returns {boolean} 是否所有映射权限都可用
     */
    checkAllRegionMappedPermissions(regionFunction) {
        if (!this.isRegionFunctionEnabled(regionFunction)) {
            return false;
        }

        const mappedPermissions = this.getRegionMappedPermissions(regionFunction);
        if (mappedPermissions.length === 0) {
            return true; // 没有映射权限，认为可用
        }

        // 这里可以添加具体的权限检查逻辑
        return true;
    }

    /**
     * 获取所有启用的映射权限
     * @returns {Array<string>} 所有启用的映射权限
     */
    getAllEnabledMappedPermissions() {
        const enabledFunctions = this.getEnabledRegionFunctions();
        const allPermissions = [];

        enabledFunctions.forEach(func => {
            const mappedPermissions = this.getRegionMappedPermissions(func);
            allPermissions.push(...mappedPermissions);
        });

        return [...new Set(allPermissions)]; // 去重
    }

    /**
     * 获取区域权限映射摘要
     * @returns {Object} 权限映射摘要
     */
    getRegionPermissionMappingSummary() {
        const enabledFunctions = this.getEnabledRegionFunctions();
        const summary = {};

        enabledFunctions.forEach(func => {
            summary[func] = {
                enabled: true,
                mappedPermissions: this.getRegionMappedPermissions(func),
                available: this.checkAllRegionMappedPermissions(func)
            };
        });

        return summary;
    }

    /**
     * 更新用户信息（重写基类方法）
     * RegionPermissionManager不依赖用户信息，所以不需要重新加载权限
     * @param {Object} userInfo - 新的用户信息
     */
    updateUserInfo(userInfo) {
        // 更新用户信息但不重新加载权限配置
        this.userInfo = { ...this.userInfo, ...userInfo };

        // 清除缓存（如果将来实现了缓存机制）
        this.clearCache();

        // 注意：不调用loadPermissions()，因为区域权限不依赖用户信息
        // 区域权限数据来自store，通过updateRegionFunctions方法更新

        console.log('RegionPermissionManager: 用户信息已更新，但权限配置保持不变');
    }

    /**
     * 销毁权限管理器
     */
    destroy() {
        // 清理区域功能配置
        this.regionFunctions = {};
        this.region = '';

        super.destroy();
    }
}

export default RegionPermissionManager;
