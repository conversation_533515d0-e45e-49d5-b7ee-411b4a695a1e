/**
 * 权限缓存机制测试文件
 * 用于验证权限系统的缓存功能是否正常工作
 */

import permissionManager from './index.js';

/**
 * 测试权限缓存机制
 */
export function testPermissionCache() {
    console.log('=== 开始测试权限缓存机制 ===');

    // 模拟用户信息
    const mockUserInfo = {
        id: 'test-user-123',
        role: 5, // 超级管理员
        name: '测试用户',
        departmentId: 'dept-001'
    };

    // 模拟区域功能配置
    const mockRegionFunctions = {
        breastAI: 1,
        live: 1,
        library: 1,
        cloudStatistic: 0,
        groupset: 1
    };

    return new Promise(async (resolve) => {
        try {
            // 1. 初始化权限管理器
            console.log('1. 初始化权限管理器...');
            await permissionManager.initializeRegionPermissions(mockRegionFunctions, 'test-region');
            await permissionManager.initialize(mockUserInfo);

            // 2. 配置缓存设置
            console.log('2. 配置缓存设置...');
            permissionManager.configureCaching({
                enableCache: true,
                defaultTTL: 30000, // 30秒缓存
                maxCacheSize: 100
            });

            // 3. 测试功能权限缓存
            console.log('3. 测试功能权限缓存...');
            await testFeaturePermissionCache();

            // 4. 测试区域权限缓存
            console.log('4. 测试区域权限缓存...');
            await testRegionPermissionCache();

            // 5. 测试组合权限缓存
            console.log('5. 测试组合权限缓存...');
            await testCombinedPermissionCache();

            // 6. 测试缓存统计
            console.log('6. 测试缓存统计...');
            testCacheStats();

            // 7. 测试缓存清理
            console.log('7. 测试缓存清理...');
            testCacheCleanup();

            console.log('=== 权限缓存机制测试完成 ===');
            resolve(true);

        } catch (error) {
            console.error('权限缓存测试失败:', error);
            resolve(false);
        }
    });
}

/**
 * 测试功能权限缓存
 */
async function testFeaturePermissionCache() {
    console.log('  测试功能权限缓存...');

    // 第一次检查 - 应该执行实际检查并缓存结果
    const start1 = performance.now();
    const result1 = permissionManager.checkFeaturePermission('backgroundManage');
    const time1 = performance.now() - start1;
    console.log(`    第一次检查 backgroundManage: ${result1}, 耗时: ${time1.toFixed(2)}ms`);

    // 第二次检查 - 应该从缓存获取
    const start2 = performance.now();
    const result2 = permissionManager.checkFeaturePermission('backgroundManage');
    const time2 = performance.now() - start2;
    console.log(`    第二次检查 backgroundManage: ${result2}, 耗时: ${time2.toFixed(2)}ms`);

    // 验证结果一致性
    if (result1 === result2) {
        console.log(`    ✓ 缓存结果一致，性能提升: ${((time1 - time2) / time1 * 100).toFixed(1)}%`);
    } else {
        console.log('    ✗ 缓存结果不一致');
    }
}

/**
 * 测试区域权限缓存
 */
async function testRegionPermissionCache() {
    console.log('  测试区域权限缓存...');

    // 第一次检查 - 应该执行实际检查并缓存结果
    const start1 = performance.now();
    const result1 = permissionManager.checkRegionPermission('breastAI');
    const time1 = performance.now() - start1;
    console.log(`    第一次检查 breastAI: ${result1}, 耗时: ${time1.toFixed(2)}ms`);

    // 第二次检查 - 应该从缓存获取
    const start2 = performance.now();
    const result2 = permissionManager.checkRegionPermission('breastAI');
    const time2 = performance.now() - start2;
    console.log(`    第二次检查 breastAI: ${result2}, 耗时: ${time2.toFixed(2)}ms`);

    // 验证结果一致性
    if (result1 === result2) {
        console.log(`    ✓ 缓存结果一致，性能提升: ${((time1 - time2) / time1 * 100).toFixed(1)}%`);
    } else {
        console.log('    ✗ 缓存结果不一致');
    }
}

/**
 * 测试组合权限缓存
 */
async function testCombinedPermissionCache() {
    console.log('  测试组合权限缓存...');

    const permission = {
        regionPermissionKey: 'breastAI',
        featurePermissionKey: 'backgroundManage'
    };

    // 第一次检查 - 应该执行实际检查并缓存结果
    const start1 = performance.now();
    const result1 = permissionManager.checkPermission(permission);
    const time1 = performance.now() - start1;
    console.log(`    第一次检查组合权限: ${result1}, 耗时: ${time1.toFixed(2)}ms`);

    // 第二次检查 - 应该从缓存获取
    const start2 = performance.now();
    const result2 = permissionManager.checkPermission(permission);
    const time2 = performance.now() - start2;
    console.log(`    第二次检查组合权限: ${result2}, 耗时: ${time2.toFixed(2)}ms`);

    // 验证结果一致性
    if (result1 === result2) {
        console.log(`    ✓ 缓存结果一致，性能提升: ${((time1 - time2) / time1 * 100).toFixed(1)}%`);
    } else {
        console.log('    ✗ 缓存结果不一致');
    }
}

/**
 * 测试缓存统计
 */
function testCacheStats() {
    console.log('  测试缓存统计...');
    
    const stats = permissionManager.getAllCacheStats();
    console.log('    缓存统计信息:', JSON.stringify(stats, null, 2));
    
    if (stats.summary.totalEntries > 0) {
        console.log('    ✓ 缓存中有数据');
    } else {
        console.log('    ✗ 缓存中没有数据');
    }
}

/**
 * 测试缓存清理
 */
function testCacheCleanup() {
    console.log('  测试缓存清理...');
    
    // 获取清理前的统计
    const statsBefore = permissionManager.getAllCacheStats();
    console.log(`    清理前缓存条目数: ${statsBefore.summary.totalEntries}`);
    
    // 清理特定模式的缓存
    permissionManager.clearCache('feature');
    
    // 获取清理后的统计
    const statsAfter = permissionManager.getAllCacheStats();
    console.log(`    清理后缓存条目数: ${statsAfter.summary.totalEntries}`);
    
    if (statsAfter.summary.totalEntries < statsBefore.summary.totalEntries) {
        console.log('    ✓ 缓存清理成功');
    } else {
        console.log('    ✗ 缓存清理失败');
    }
}

/**
 * 性能对比测试
 */
export function performanceComparisonTest() {
    console.log('=== 开始性能对比测试 ===');

    const iterations = 1000;
    const permission = 'backgroundManage';

    // 禁用缓存测试
    permissionManager.configureCaching({ enableCache: false });
    
    const startWithoutCache = performance.now();
    for (let i = 0; i < iterations; i++) {
        permissionManager.checkFeaturePermission(permission);
    }
    const timeWithoutCache = performance.now() - startWithoutCache;

    // 启用缓存测试
    permissionManager.configureCaching({ enableCache: true });
    
    // 预热缓存
    permissionManager.checkFeaturePermission(permission);
    
    const startWithCache = performance.now();
    for (let i = 0; i < iterations; i++) {
        permissionManager.checkFeaturePermission(permission);
    }
    const timeWithCache = performance.now() - startWithCache;

    console.log(`无缓存 ${iterations} 次检查耗时: ${timeWithoutCache.toFixed(2)}ms`);
    console.log(`有缓存 ${iterations} 次检查耗时: ${timeWithCache.toFixed(2)}ms`);
    console.log(`性能提升: ${((timeWithoutCache - timeWithCache) / timeWithoutCache * 100).toFixed(1)}%`);

    console.log('=== 性能对比测试完成 ===');
}

// 如果在浏览器环境中，添加到全局对象以便调试
if (typeof window !== 'undefined') {
    window.testPermissionCache = testPermissionCache;
    window.performanceComparisonTest = performanceComparisonTest;
}
