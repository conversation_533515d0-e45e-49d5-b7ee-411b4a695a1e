<template>
    <div>
        <base-menu :menuShow.sync="isShowMenuVisible" ref="image_menu" :eventData="eventData" :menuItems="menuItems">
        </base-menu>
        <ReviewEditDialog :message="currentFile" ref="reviewEditDialog"></ReviewEditDialog>
        <template v-if="cloudVideoEditParentVisible">
            <CloudVideoEditParent
                ref="CloudVideoEditParent"
                :cid="currentEditEnvCid"
                :resourceId="currentFile.from === 'personalFavorite' ? currentFile.resource_copied_from : currentFile.resource_id"
                :userId="user.uid"
                v-model="cloudVideoEditParentVisible"
            ></CloudVideoEditParent>
        </template>
        <SelectFavoriteType :message="currentFile" v-model="isShowSelectFavoriteDialog"></SelectFavoriteType>
        <ImageRenameDialog :message="currentFile" v-model="imageRenameVisible"></ImageRenameDialog>
        <ShareEmailDialog :message="currentFile" v-model="shareEmailVisible"></ShareEmailDialog>
    </div>
</template>
<script>
import base from "../lib/base";
import { findServiceId } from "../lib/common_base";
import { uniq } from "lodash";
import { initImportExamImageTempQueue, checkIsCreator, checkIsManager, getResourceTempState } from "../lib/common_base";
import service from "../service/service";
import ReviewEditDialog from "./messageItem/reviewEditDialog.vue";
import ImageRenameDialog from "./messageItem/imageRenameDialog.vue";
import ShareEmailDialog from "./messageItem/shareEmailDialog.vue";
import CloudVideoEditParent from "../pages/cloudVideoEdit/cloudVideoEditParent.vue";
import Tool from "@/common/tool";
import SelectFavoriteType from "./selectFavoriteType.vue";
import BaseMenu from "./baseMenu.vue";
import { htmlEscape } from "../lib/common_base";
import permissionManager from "@/common/permission/PermissionManager";
export default {
    mixins: [base],
    name: "ImageMenu",
    components: {
        ReviewEditDialog,
        CloudVideoEditParent,
        ImageRenameDialog,
        SelectFavoriteType,
        ShareEmailDialog,
        BaseMenu,
    },
    data() {
        return {
            isShowMenuVisible: false,
            currentFile: null,
            downloadFileName: "",
            downloadNum: "",
            downloadDirectory: "",
            ai_share_to_group: false, //AI分析消息是否自动发送到群内
            showSendToAnalyze: false, //是否显示AI分析按钮
            showTransmitCommit: false, //是否显示转发按钮
            showSaveFavorite: false, //是否显示收藏按钮
            showDeleteImage: false, //是否显示删除按钮
            showImageSearch: false, //是否显示以图搜图
            showDownloadImage: false, //是否显示下载按钮
            showDeleteExam: false, //是否显示删除检查按钮
            showSetResourceExamInfo: false, //是否显示设置检查信息按钮
            showShareEmail: false, //是否显示分享右键按钮
            showClip: false, //是否显示裁剪按钮
            showWithdrawMessage: false, // 撤回按钮
            showQuote: false, // 引用按钮
            currentData: {}, //当前传过来的参数
            showDeleteGroupFavoriteImage: false, //是否显示删除群收藏按钮
            showEditReview: false, //是否显示回放编辑按钮
            showRename: false, //显示重命名按钮
            showVideoCloudEditModal: false, //是否显示视频云剪辑按钮
            cloudVideoEditParentVisible: false,
            currentEditEnvCid: 0, //当前云剪辑环境所处cid
            imageRenameVisible: false,
            shareEmailVisible: false,
            isShowSelectFavoriteDialog: false, //云收藏选择类型弹窗
            isSelf: false,
            isCreator: false,
            isManager: false,
            eventData: {
                clientX: 0,
                clientY: 0,
            }, // 初始化为符合验证器要求的对象
        };
    },
    computed: {
        cid() {
            return this.$route.params.cid;
        },
        conversation() {
            return this.conversationList[this.cid];
        },
        isInternalNetworkEnv() {
            return this.systemConfig.serverInfo.network_environment;
        },
        menuItems() {
            const items = [];

            if (this.showSendToAnalyze) {
                items.push({
                    label: this.$t('action_analyze_text'),
                    handler: this.sendToAnalyze,
                });
            }

            if (this.showTransmitCommit) {
                items.push({
                    label: this.$t('transmit_title'),
                    handler: this.transmitCommit,
                });
            }

            if (this.showQuote) {
                items.push({
                    label: this.$t('quote_title'),
                    handler: this.quote,
                });
            }

            if (this.showSaveFavorite) {
                items.push({
                    label: this.$t('action_favorite_text'),
                    handler: this.showSelectFavoriteDialog,
                });
            }

            if (this.showDeleteImage) {
                items.push({
                    label: this.$t('action_delete_text'),
                    handler: this.deleteImage,
                });
            }

            if (this.showDeleteExam) {
                items.push({
                    label: this.$t('delete_case'),
                    handler: this.handleDeleteExam,
                });
            }

            if (this.showDeleteGroupFavoriteImage) {
                items.push({
                    label: this.$t('action_delete_text'),
                    handler: this.deleteGroupFavoriteImage,
                });
            }

            if (this.showDownloadImage) {
                items.push({
                    label: this.$t('download_title'),
                    handler: this.downloadImage,
                });
            }

            if (this.showSetResourceExamInfo) {
                items.push({
                    label: this.$t('action_set_exam_info_text'),
                    handler: this.setResourceExamInfo,
                });
            }

            if (this.showClip) {
                items.push({
                    label: this.$t('action_clip_text'),
                    handler: this.clip,
                });
            }

            if (this.showWithdrawMessage) {
                items.push({
                    label: this.$t('revocation_message'),
                    handler: this.withdrawMessage,
                });
            }

            if (this.showEditReview) {
                items.push({
                    label: this.$t('edit_review_info'),
                    handler: this.openEditReviewModal,
                });
            }

            if (this.showVideoCloudEditModal) {
                items.push({
                    label: this.$t('video_clips'),
                    handler: this.openVideoCloudEditModal,
                });
            }

            if (this.showImageSearch) {
                items.push({
                    label: this.$t('searc_in_case_database'),
                    handler: this.searchInCaseData,
                });
            }

            if (this.showRename) {
                items.push({
                    label: this.$t('rename'),
                    handler: this.openRenameModal,
                });
            }

            if (this.showShareEmail) {
                items.push({
                    label: this.$t('share_email_title'),
                    handler: this.openShareEmailModal,
                });
            }

            return items;
        },
    },
    mounted() {
        this.$nextTick(() => {
            // 监听全局点击事件
            this.$root.eventBus.$off("showImageMenu").$on("showImageMenu", (data) => {
                console.log(data.from, data);
                if (data.file && data.file.gmsg_id) {
                    this.isShowMenuVisible = true;
                    this.currentData = data;
                    this.currentFile = Object.assign({}, data.file);
                    this.checkMenusItemShow(data);

                    // 处理事件数据，确保符合baseMenu验证器要求
                    if (data.event) {
                        // 确保eventData至少包含clientX和clientY属性
                        const eventData = {
                            clientX: data.event.clientX !== undefined ? data.event.clientX : data.event.center?.x || 0,
                            clientY: data.event.clientY !== undefined ? data.event.clientY : data.event.center?.y || 0,
                        };

                        // 如果原事件有center属性，保留它
                        if (data.event.center) {
                            eventData.center = data.event.center;
                        }

                        this.eventData = eventData;
                    } else {
                        // 如果没有事件数据，使用默认值
                        this.eventData = {
                            clientX: 0,
                            clientY: 0,
                        };
                    }

                    // this.$nextTick(() => {
                    //     this.$refs.base_menu.checkMenuPosition(data);
                    // });
                }
            });
            this.$root.eventBus.$off("generalTransmitSubmit").$on("generalTransmitSubmit", this.generalTransmitSubmit);
            this.$root.eventBus.$off("sendTransmitMessage").$on("sendTransmitMessage", this.sendTransmitMessage);
            this.$root.eventBus.$off("openFavoriteDialog").$on("openFavoriteDialog", this.openFavoriteDialog);
        });
    },
    methods: {
        initAllShowBtn() {
            this.showSendToAnalyze = false; //是否显示图像处理按钮
            this.showTransmitCommit = false; //是否显示转发按钮
            this.showSaveFavorite = false; //是否显示收藏按钮
            this.showDeleteImage = false; //是否显示删除按钮
            this.showImageSearch = false; //是否显示以图搜图
            this.showDownloadImage = false; //是否显示下载按钮
            this.showDeleteExam = false; //是否显示删除检查按钮
            this.showSetResourceExamInfo = false; //是否显示设置检查信息按钮
            this.showClip = false; //是否显示裁剪按钮
            this.showWithdrawMessage = false; // 撤回按钮
            this.showDeleteGroupFavoriteImage = false; //是否显示删除群收藏按钮
            this.showEditReview = false; //是否显示回放编辑按钮
            this.showRename = false; //显示重命名按钮
            this.showVideoCloudEditModal = false; //是否显示视频云剪辑按钮
            this.showShareEmail = false;
            this.showQuote = false; // 是否显示引用按钮
        },
        checkMenusItemShow(data) {
            this.initAllShowBtn();
            this.isSelf = this.user.uid === data.file.sender_id;
            this.isCreator = false;
            this.isManager = false;
            if (this.conversation) {
                this.isCreator = checkIsCreator(this.cid);
                this.isManager = checkIsManager(this.cid);
            }
            const msg_type = data.file.msg_type;
            const msgType = this.systemConfig.msg_type;
            if (msg_type === msgType.Image || msg_type === msgType.Frame || msg_type === msgType.OBAI) {
                this.showTransmitCommit = true;
                if (data.from !== "user_favorites") {
                    this.showSaveFavorite = true;
                    this.showQuote = this.checkShowQuote();
                    this.showSendToAnalyze = this.checkShowSendToAnalyze(data);
                }
                this.showDownloadImage = true;
                this.showImageSearch = this.checkShowImageSearch();
                if (this.checkShowItemWhenHasPermission(data)) {
                    this.handleShowDeleteImage();
                }
                if (this.checkShowRenameHasPermission(data)) {
                    this.showRename = true;
                }
            } else if (msg_type == msgType.TAG || msg_type == msgType.COMMENT) {
                if (this.isSelf) {
                    this.showWithdrawMessage = this.checkShowWithDrawOrDelete();
                }
            } else if (msg_type === msgType.Video || msg_type === msgType.Cine) {
                this.showTransmitCommit = true;
                if (data.from !== "user_favorites") {
                    this.showSaveFavorite = true;
                    this.showQuote = this.checkShowQuote();
                }
                this.showDownloadImage = true;
                if (this.checkShowItemWhenHasPermission(data)) {
                    this.handleShowDeleteImage();
                }
                if (this.checkShowRenameHasPermission(data)) {
                    this.showRename = true;
                }
            } else if (msg_type === msgType.RealTimeVideoReview) {
                if (!this.isInternalNetworkEnv && !this.globalParams.isCE) {
                    this.showVideoCloudEditModal = true;
                }

                this.showTransmitCommit = true;
                if (data.from !== "user_favorites") {
                    this.showSaveFavorite = true;
                    this.showQuote = this.checkShowQuote();
                }
                this.showDownloadImage = true;
                this.isSelf = this.user.uid === data.file.live_record_data.creator_id;
                if (this.checkShowItemWhenHasPermission(data)) {
                    this.handleShowDeleteImage();
                    if (data.from !== "user_favorites") {
                        this.showEditReview = true;
                    }
                }
            } else if (msg_type === msgType.VIDEO_CLIP) {
                this.showTransmitCommit = true;
                if (data.from !== "user_favorites") {
                    this.showSaveFavorite = true;
                    this.showQuote = this.checkShowQuote();
                }
                this.showDownloadImage = true;
                if (!this.isInternalNetworkEnv && !this.globalParams.isCE) {
                    this.showVideoCloudEditModal = true;
                }
                if (this.checkShowItemWhenHasPermission(data)) {
                    this.handleShowDeleteImage();
                }
            } else if (msg_type === msgType.LIVE_INVITE) {
                this.showTransmitCommit = true;
                if (this.checkShowItemWhenHasPermission(data)) {
                    this.handleShowDeleteImage();
                }
            } else if (msg_type === msgType.IWORKS_PROTOCOL) {
                this.showTransmitCommit = true;
                this.showDownloadImage = true;
                if (this.checkShowItemWhenHasPermission(data)) {
                    this.handleShowDeleteImage();
                }
            } else if (msg_type === msgType.File) {
                if (!data.file.uploading) {
                    if (!this.checkFileExpired(data.file)) {
                        this.showTransmitCommit = true;
                        this.showDownloadImage = true;
                    }
                    if (this.checkShowItemWhenHasPermission(data)) {
                        this.handleShowDeleteImage();
                    }
                }
            } else if (msg_type === msgType.AI_ANALYZE) {
                if (true) {
                    this.showTransmitCommit = true;

                    if (this.isSelf) {
                        this.showWithdrawMessage = this.checkShowWithDrawOrDelete();
                    }
                    if (data.file && (data.file.been_withdrawn || !data.file.resource_id)) {
                        this.showTransmitCommit = false;
                    }
                }
            } else if (msg_type === msgType.EXAM_IMAGES) {
                this.showTransmitCommit = true;
                this.showShareEmail = true;
                this.showSendToAnalyze = this.checkShowSendToAnalyze(data);
            } else if (msg_type === msgType.Sound) {
                if (this.isSelf) {
                    this.showWithdrawMessage = this.checkShowWithDrawOrDelete();
                }
            } else if (msg_type === msgType.HOMEWORK_DETAIL) {
                this.showTransmitCommit = true;
                if (this.isSelf) {
                    this.showWithdrawMessage = this.checkShowWithDrawOrDelete();
                }
            }
        },
        checkShowItemWhenHasPermission(data) {
            if (!data.from) {
                return false;
            }
            if (data.from === "user_favorites") {
                return true;
            }
            if (data.from.indexOf("chatComponent") > -1 || data.from === "groupAllFileList") {
                if (this.isSelf || this.isCreator || this.isManager) {
                    return true;
                }
            } else if (data.from === "groupFavorite") {
                if (data.file.creator_id === this.user.uid || this.isCreator || this.isManager) {
                    return true;
                }
            }
            return false;
        },
        checkShowRenameHasPermission(data) {
            if (!data.from) {
                return false;
            }
            if (data.from.indexOf("chatComponent") > -1 || data.from === "groupAllFileList") {
                if (this.isSelf || this.isCreator || this.isManager) {
                    return true;
                }
            } else if (data.from === "groupFavorite") {
                console.log(data);
                if (data.file.creator_id === this.user.uid || this.isCreator || this.isManager) {
                    return true;
                }
            }
            return false;
        },
        handleShowDeleteImage() {
            if (this.currentData.from === "groupFavorite") {
                this.showDeleteGroupFavoriteImage = true;
            } else {
                this.showWithdrawMessage = this.checkShowWithDrawOrDelete();
                this.showDeleteImage = !this.showWithdrawMessage;
            }
        },
        checkShowSendToAnalyze(data) {
            let isNotAiAnalyzeServiceType = false;

            if (this.conversation) {
                isNotAiAnalyzeServiceType =
                    this.$store.state.systemConfig.user_service_type.AiAnalyze != this.conversation.service_type;
            }
            if (data.file && (data.file.been_withdrawn || !data.file.resource_id)) {
                isNotAiAnalyzeServiceType = false;
            }
            return (
                permissionManager.checkPermission({
                    regionPermissionKey: 'breastAI',
                }) &&
                this.systemConfig.serverInfo.enable_ai_analyze &&
                !this.globalParams.isCE &&
                isNotAiAnalyzeServiceType
            );
        },
        checkShowWithDrawOrDelete() {
            //撤回和删除显示逻辑
            let msg = this.currentFile;
            let showWithdrawMessage = false;
            let isSelf = this.user.uid === msg.sender_id;
            if (this.currentData.from !== "chatComponent") {
                return showWithdrawMessage;
            }
            if (!isSelf) {
                return showWithdrawMessage;
            }
            if (msg && Object.keys(msg).length > 0) {
                let msgTimestamp = new Date(msg.send_ts).getTime();
                let nowTimestamp = new Date().getTime();
                let isInnerTime = nowTimestamp - msgTimestamp < this.systemConfig.serverInfo.msg_withdrawal_max_time;
                if (isInnerTime) {
                    showWithdrawMessage = true;
                }
            }
            return showWithdrawMessage;
        },
        checkShowImageSearch() {
            let enable_ai_search =
                this.$store.state.systemConfig.serverInfo.ai_searcher_server &&
                this.$store.state.systemConfig.serverInfo.ai_searcher_server.enable &&
                !this.globalParams.isCE;
            return permissionManager.checkPermission({
                regionPermissionKey: 'breastCases',
            }) && enable_ai_search;
        },
        checkShowQuote() {
            if (this.currentData.from === "chatComponent") {
                return this.currentFile.gmsg_id ? true : false;
            }
            return false;
        },
        transmitCommit() {
            this.isShowMenuVisible = false;

            if (this.currentFile.msg_type === this.systemConfig.msg_type.LIVE_INVITE) {
                this.$root.eventBus.$emit("openTransmit", {
                    callback: this.generalLiveTransmitSubmit,
                    cid: this.cid,
                });
            } else if (this.currentFile.msg_type === this.systemConfig.msg_type.HOMEWORK_DETAIL) {
                const { _id } = this.currentFile.assignmentInfo;
                service
                    .getanswerSheetByAssignmentID({
                        assignmentID: _id,
                    })
                    .then((res) => {
                        if (res.data.error_code === 0) {
                            const assignmentInfo = res.data.data.assignmentInfo;
                            if (assignmentInfo && assignmentInfo.dueTime) {
                                if (Date.now() > assignmentInfo.dueTime) {
                                    // 检查当前用户是否为群主和管理员，只有管理者可以修改作业配置
                                    if (this.isCreator || this.isManager) {
                                        this.$MessageBox
                                            .confirm(this.$t('exam_overdue_creator_tip'), this.$t('tip_title'), {
                                                confirmButtonText: this.$t('confirm_txt'),
                                                cancelButtonText: this.$t('cancel_btn'),
                                                type: "warning",
                                            })
                                            .then(() => {
                                                this.$store.commit("homework/setCurrentPaper", assignmentInfo);
                                                this.$router.push(this.$route.path + "/exam_setting");
                                            })
                                            .catch(() => {});
                                    } else {
                                        this.$message.error(this.$t('overdue_assignment_transmit_tip'));
                                    }
                                    return;
                                }

                                this.$root.eventBus.$emit("openTransmit", {
                                    cid: 0,
                                    disableChat: true,
                                    disableFriend: true,
                                    disableGroup: false,
                                    isServiceTypeNone: 1,
                                    callback: (data) => {
                                        service
                                            .transmitHomework({
                                                gid: data.cid,
                                                assignmentID: _id,
                                            })
                                            .then((res) => {
                                                if (res.data.error_code === 0) {
                                                    this.$message.success(this.$t('operate_success'));
                                                }
                                            });
                                    },
                                });
                            } else {
                                this.$message.error(this.$t('operate_err'));
                            }
                        } else {
                            this.$message.error(this.$t('operate_err'));
                        }
                    })
                    .catch(() => {
                        this.$message.error(this.$t('operate_err'));
                    });
            } else {
                let queue = [];
                if (this.currentFile.msg_type == this.systemConfig.msg_type.AI_ANALYZE) {
                    if (this.currentFile.ai_analyze && this.currentFile.ai_analyze.messages) {
                        queue = this.currentFile.ai_analyze.messages || [];
                    }
                } else {
                    if (!this.currentFile.url) {
                        this.$message.error(this.$t('operate_err'));
                        return;
                    }
                    if (this.currentFile.resource_copied_from) {
                        this.currentFile.resource_id = this.currentFile.resource_copied_from;
                    }
                    queue.push(this.currentFile);
                }
                this.$root.transmitTempList = queue;
                this.$root.eventBus.$emit("openTransmit", {
                    callback: this.generalTransmitSubmit,
                    cid: this.cid,
                });
            }
            // this.$router.push(this.$route.fullPath+'/transmit')
        },
        deleteImage() {
            // console.log(data.from)
            if (this.currentFile.is_private) {
                this.$root.eventBus.$emit("deleteFavoritesImage", this.currentFile);
            } else {
                let cid = this.currentFile.group_id;
                // this.tryToDeleteMessages(cid,[this.currentFile])
                let gmsg_id = 0;
                if (this.currentData.from === "chatComponent") {
                    gmsg_id = this.currentFile.gmsg_id;
                }
                this.deleteResourceByGroupId(this.currentFile, gmsg_id);
            }
            this.isShowMenuVisible = false;
        },
        generalLiveTransmitSubmit(data) {
            this.sendLiveTransmitMessage(data);
        },
        generalTransmitSubmit(data) {
            let queue = this.$root.transmitTempList;
            this.ai_share_to_group = data.ai_share_to_group;
            Object.entries(this.conversationList).map((item) => {
                // 判断会话列表中是否有与fid对应的会话 有则直接取之前的cid
                if (data.id && item[1].fid === data.id) {
                    data.cid = item[0];
                }
            });
            this.$root.transmitQueue[data.cid || "f-" + data.id] = queue;
            if (this.conversationList[data.cid]) {
                //会话已开启则直接转发
                this.sendTransmitMessage(data.cid);
            } else {
                //会话未开启则开启会话
                if (data.cid) {
                    this.openConversation(data.cid, 10);
                } else {
                    this.openConversation(data.id, 3);
                }
            }
        },
        sendLiveTransmitMessage(data) {
            let params = {
                live_id: this.currentFile.liveInfo.live_id,
            };
            if (data.from === "friend") {
                params.rc_type = 1;
                params.rc_id = data.uid;
            } else if (data.from === "group") {
                params.rc_type = 2;
                params.rc_id = data.cid;
            }

            window.main_screen.inviteOthersLive(params, (res) => {
                if (!res.error_code) {
                    // this.liveHistoryList = res.data
                    this.$message.success(this.$t('machine_transmit_tip'));
                } else {
                    this.$message.error(res.error_msg);
                }
            });
        },
        sendTransmitMessage(cid) {
            let that = this;
            let conversation = this.conversationList[cid];
            let queue = this.$root.transmitQueue[cid];
            let isToAI = conversation.service_type == this.systemConfig.ServiceConfig.type.AiAnalyze;
            let hasVideo = false;
            var arr = [];
            let ifAiMsg = false;
            for (let msg of queue) {
                if (msg.msg_type == this.systemConfig.msg_type.AI_ANALYZE) {
                    arr.push({
                        type: this.systemConfig.msg_type.AI_ANALYZE,
                        id: msg.ai_analyze.ai_analyze_id,
                    });
                    ifAiMsg = true;
                } else if (msg.msg_type == this.systemConfig.msg_type.EXAM_IMAGES) {
                    let temp = [];
                    let effResourceList = msg.resourceList.filter((item) => {
                        if (msg.imageList) {
                            return msg.imageList.some((item2) => {
                                if (item2.resource_id == item.id) {
                                    if (getResourceTempState(item2.resource_id) === 1) {
                                        return true;
                                    }
                                }
                            });
                        } else {
                            if (getResourceTempState(item.id) === 1) {
                                return true;
                            }
                        }
                    });
                    for (let resource of effResourceList) {
                        if (isToAI && resource.resource_type == 2) {
                            //忽略发送给AI的视频
                            hasVideo = true;
                            continue;
                        } else {
                            temp.unshift(resource.id);
                        }
                    }
                    arr = arr.concat(temp);
                } else if (
                    msg.msg_type == this.systemConfig.msg_type.Video ||
                    msg.msg_type == this.systemConfig.msg_type.Cine ||
                    msg.msg_type == this.systemConfig.msg_type.RealTimeVideoReview ||
                    msg.msg_type == this.systemConfig.msg_type.VIDEO_CLIP
                ) {
                    if (isToAI) {
                        //忽略发送给AI的视频
                        hasVideo = true;
                        continue;
                    } else {
                        arr.push(msg.resource_id);
                    }
                } else {
                    arr.push(msg.resource_id);
                }
            }
            arr = uniq(arr);
            delete this.$root.transmitQueue[cid];
            if (hasVideo) {
                this.$message.error(this.$t('can_not_analyze_video'));
                return;
            }
            if (arr.length == 0) {
                this.$message.error(this.$t('transmit_fail_tip'));
                return;
            }
            let share_group = 0;
            if (!ifAiMsg && isToAI) {
                for (let item of queue) {
                    share_group = item.group_id;
                }
                this.analyzeAction(cid, arr, share_group);
            } else {
                //图片消息队列
                var controller = conversation.socket;
                controller.emit("sendto", arr, function (is_succ, data) {
                    console.log("sendto", is_succ, data);
                    if (is_succ) {
                    } else {
                        that.$message.error(that.$t('transmit_fail_tip'));
                    }
                });
                this.$message.success(this.$t('transmiting_tip'));
            }
            delete this.$root.transmitQueue[cid];
        },
        analyzeAction(cid, queue, share_group) {
            let conversation = this.conversationList[cid];
            var controller = conversation.socket;
            let subscribers = [];
            if (this.ai_share_to_group && share_group != cid) {
                subscribers.push({
                    type: 2,
                    group_id: share_group,
                });
                controller.emit("add_ai_analyze", {
                    resource_id_list: queue,
                    subscribers: subscribers,
                });
            }
            this.$message.success(this.$t('sned_to_analyze_tip'));
        },
        downloadImage() {
            if (!this.currentFile.url) {
                this.$message.error(this.$t('operate_err'));
                return;
            }
            let url = this.getDownloadUrl(this.currentFile);
            let filename = this.currentFile.file_name || "";
            if (this.globalParams.isCef) {
                let surl = window.location.origin;
                if (url.indexOf("http") != 0) {
                    url = surl + "/" + url;
                }
                this.currentFile.downloadUrl = url;
                this.$root.eventBus.$emit("createDownLoadTask", {
                    downLoadUrlList: [{ src: url, filename, msg_type: this.currentFile.msg_type }],
                    needsInputFileName: true,
                });
            } else {
                console.error(url, Tool.getFileType(url));
                if (Tool.getFileType(url) === "m3u8") {
                    this.$message.error(this.$t('use_app_tip'));
                } else {
                    Tool.openLinkByDefaultBrowser(encodeURI(url));
                }
            }
            this.isShowMenuVisible = false;
        },
        sendToAnalyze: async function () {
            if (!this.currentFile.url) {
                this.$message.error(this.$t('operate_err'));
                return;
            }
            let analyze = await findServiceId(this.systemConfig.ServiceConfig.type.AiAnalyze);
            analyze.ai_share_to_group = true;
            let queue = [];
            queue.push(this.currentFile);
            this.$root.transmitTempList = queue;
            this.generalTransmitSubmit(analyze);
            this.isShowMenuVisible = false;
        },
        setResourceExamInfo() {
            var that = this;
            let list = [that.currentFile];
            initImportExamImageTempQueue(list, function (err) {
                if (!err) {
                    that.$router.push(that.$route.fullPath + "/exam_manager");
                }
            });
            this.isShowMenuVisible = false;
        },
        clip() {
            var that = this;
            var image_copy = Object.assign({}, that.currentFile);
            //image_copy.import_exam_image = true;  //通知播放器进入裁剪模式
            image_copy.loaded = true;

            that.$store.commit("gallery/setGallery", {
                list: [image_copy],
                openFile: image_copy,
            });
            that.$nextTick(() => {
                that.$router.push(that.$route.fullPath + "/clip_gallery");
            });
        },
        withdrawMessage() {
            let msg = this.currentFile;
            if (msg && Object.keys(msg).length > 0) {
                let msgTimestamp = new Date(msg.send_ts).getTime();
                let nowTimestamp = new Date().getTime();
                let isInnerTime = nowTimestamp - msgTimestamp < this.systemConfig.serverInfo.msg_withdrawal_max_time;
                let isSelf = this.user.uid === msg.sender_id;
                if (isInnerTime && isSelf) {
                    this.tryToWithDrawMessages(this.cid, [msg]);
                } else {
                    this.$message.error(this.$t('exceeded_max_withdrawal'));
                }
            }
            this.isShowMenuVisible = false;
        },
        deleteGroupFavoriteImage() {
            this.$root.eventBus.$emit("removeResourceItemFromCategory", this.currentFile);
            this.isShowMenuVisible = false;
        },
        openEditReviewModal() {
            this.isShowMenuVisible = false;
            let cid = this.currentFile.group_id;
            service
                .checkActionPermissions({
                    action: "conference.update.recording",
                    businessData: {
                        resource_id: this.currentFile.resource_id,
                        group_id: cid,
                    },
                })
                .then((res) => {
                    if (res.data.error_code === 0) {
                        if (!res.data.data.hasPermission) {
                            this.$message.error(this.$t('no_permission_operate'));
                            return;
                        }
                        this.$nextTick(() => {
                            this.$refs[`reviewEditDialog`].openEditTargetModal();
                        });
                    } else {
                        this.$message.error(this.$t(res.data.key));
                    }
                })
                .catch((error) => {
                    console.error(error);
                });
        },
        openFavoriteDialog(data) {
            this.$root.eventBus.$emit("hideRealTimeVideo");
            this.isShowSelectFavoriteDialog = true;
            this.currentFile = Object.assign({}, data);
        },
        showSelectFavoriteDialog() {
            this.isShowSelectFavoriteDialog = true;
            this.isShowMenuVisible = false;
        },
        openVideoCloudEditModal() {
            this.isShowMenuVisible = false;
            this.currentEditEnvCid = this.currentFile.group_id || 0;

            this.cloudVideoEditParentVisible = true;
        },
        searchInCaseData() {
            this.isShowMenuVisible = false;
            if (this.currentFile) {
                let cid = this.currentFile.group_id;
                let searchParams = {
                    type: "url", //text,url,file
                    content: this.currentFile,
                };
                let islegal = Tool.isLegalForematForSearchImage(this.currentFile);
                if (islegal) {
                    this.$store.commit("caseDatabase/updateSearchParams", searchParams);
                    this.$router.push({ path: `/main/index/chat_window/${cid}/case_database` });
                } else {
                    this.$message.error(this.$t('picture_is_only_jpg_jpeg_bmp_png'));
                }
            }
        },
        clickoutside() {
            this.isShowMenuVisible = false;
        },
        openRenameModal() {
            this.imageRenameVisible = true;
            this.isShowMenuVisible = false;
        },
        handleDeleteExam() {
            if (!this.currentFile.hasOwnProperty("group_id")) {
                this.currentFile.group_id = this.cid;
            }

            this.isShowMenuVisible = false;
            this.$MessageBox
                .confirm(this.$t('delete_exam_tips'), this.$t('tip_title'), {
                    confirmButtonText: this.$t('confirm_txt'),
                    cancelButtonText: this.$t('cancel_btn'),
                    closeOnClickModal: false,
                })
                .then(() => {
                    this.deleteExam(this.currentFile);
                })
                .catch((e) => {});
        },
        openShareEmailModal() {
            this.shareEmailVisible = true;
            this.isShowMenuVisible = false;
        },
        quote() {
            if (!this.currentFile || !this.currentFile.gmsg_id) {
                return;
            }
            let msg = this.currentFile
            this.isShowMenuVisible = false;
            let quoteMessage = {
                msg_body:htmlEscape(msg.original_msg_body),
                gmsg_id:msg.gmsg_id,
                msg_type:msg.msg_type,
                sender_id:msg.sender_id,
                nickname:msg.nickname || '',
                send_ts:msg.send_ts || '',
                resource_id:msg.resource_id || '',
                url:msg.url|| '',
                avatar:msg.avatar|| '',
                thumb:msg.thumb|| '',
                img_id:msg.img_id|| '',
                img_has_gesture_video:msg.img_has_gesture_video|| '',
                file_id:msg.file_id|| '',
                file_name:msg.file_name|| '',
                group_id:msg.group_id|| '',
                original_file_name:msg.original_file_name|| '',
                ultrasound_url: msg.ultrasound_url || '',
                mp4FileUrl: msg.mp4FileUrl || '',
            }
            this.$root.eventBus.$emit("quoteMessage", quoteMessage);
        },
    },
};
</script>
