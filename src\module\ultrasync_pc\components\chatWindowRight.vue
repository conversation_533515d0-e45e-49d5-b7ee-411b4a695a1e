<template>
    <div class="chat_window_right" :key="'chat_window_right_' + cid">
        <div class="tabs">
            <div
                :class="{ active: tab == 1 }"
                :title="$t('group_favorite_text')"
                class="tab1 tabItem"
                @click="toggleTab(1)"
            >
                <i class="icon iconfont iconstar2"></i>
                <span>{{ $t('group_favorite_text') }}</span>
            </div>

            <div :class="{ active: tab == 2 }" :title="$t('group_all_file')" class="tab2 tabItem" @click="toggleTab(2)">
                <i class="icon iconfont iconquanbuwenjian-weixuanzhong"></i>
                <span>{{ $t('group_all_file') }}</span>
            </div>
            <!-- <span :class="{active:tab==3}" class="tab3" :title="$t('group_conversion_file')" @click="toggleTab(3)"></span> -->
            <!-- <span :class="{active:tab==4}" class="tab4" :title="$t('group_realtime_review')" @click="toggleTab(4)"></span> -->
        </div>
        <div class="group_files">
            <vue-scroll v-show="tab == 1">
                <group-favorite
                    :edit="false"
                    :cid="cid"
                    v-if="conversation.socket"
                    ref="chatRightGroupFavorite"
                ></group-favorite>
            </vue-scroll>
            <template v-if="tab == 2">
                <el-tabs v-model="allFileActiveTab" class="all_files_container" @tab-click="handleClickTab">
                    <el-tab-pane :label="$t('group_all_file')" name="all">
                        <gallery-file-list
                            v-if="allFileActiveTab === 'all'"
                            @loadMore="loadMore('all')"
                            :galleryList="allFileList"
                            :span="1"
                            :finished="galleryObj.gallery_index < 0"
                            v-loading="galleryObj.gallery_index === 0"
                            @openGallery="openGallery($event, 'all')"
                            ref="allFileList"
                            from="groupAllFileList"
                        >
                        </gallery-file-list>
                    </el-tab-pane>
                    <el-tab-pane :label="$t('library_image')" name="image">
                        <div>
                            <gallery-file-list
                                v-if="allFileActiveTab === 'image'"
                                @loadMore="loadMore('image')"
                                :galleryList="imageFileList"
                                :span="1"
                                :finished="galleryObj.image_index < 0"
                                v-loading="galleryObj.image_index === 0"
                                @openGallery="openGallery($event, 'image')"
                                ref="imageFileList"
                                from="groupAllFileList"
                            >
                            </gallery-file-list>
                        </div>
                    </el-tab-pane>
                    <el-tab-pane :label="$t('library_video')" name="video">
                        <div>
                            <gallery-file-list
                                v-if="allFileActiveTab === 'video'"
                                @loadMore="loadMore('video')"
                                :galleryList="videoFileList"
                                :span="1"
                                :finished="galleryObj.video_index < 0"
                                v-loading="galleryObj.video_index === 0"
                                @openGallery="openGallery($event, 'video')"
                                ref="videoFileList"
                                from="groupAllFileList"
                            >
                            </gallery-file-list>
                        </div>
                    </el-tab-pane>
                    <el-tab-pane :label="$t('group_conversion_file')" name="exam">
                        <div>
                            <gallery-file-list
                                v-if="allFileActiveTab === 'exam'"
                                @loadMore="loadMore('exam')"
                                :galleryList="examFileList"
                                :span="1"
                                :finished="galleryObj.exam_index < 0"
                                v-loading="galleryObj.exam_index === 0"
                                @openGallery="openGallery($event, 'exam')"
                                ref="examFileList"
                                from="groupAllFileList"
                            >
                            </gallery-file-list>
                        </div>
                    </el-tab-pane>
                </el-tabs>
            </template>
            <template v-if="tab == 3">
                <gallery-file-list :galleryList="examFile" :span="2"></gallery-file-list>
            </template>
            <!-- <template v-if="tab==4">
                <gallery-file-list :galleryList="realtimeReview" :span="2"></gallery-file-list>
            </template> -->
        </div>
        <div class="group_attendee" v-show="conversation.is_single_chat == 0" v-drag-resize="{ directions: ['top'],minHeight: 200, maxHeight: 600 }">
            <div class="title_wrapper">
                <p
                    class="title longwrap"
                    :class="{ title_active: isTitleActive === 1 }"
                    @click="changeGroupAttendeeShow(1)"
                >
                    <i class="icon iconfont icongroups"></i>
                    {{ $t('attendee_member_text') }}({{ onlineAttendeeNum }}/{{ attendeeArray.length }})
                </p>
                <p
                    class="title longwrap"
                    :class="{ title_active: isTitleActive === 2 }"
                    @click="changeGroupAttendeeShow(2)"
                >
                    <i class="icon iconfont iconbijiben"></i>
                    {{ $t('device_list') }}({{ deviceArray.length }})
                    <i class="icon iconfont iconjinggao" v-if="deviceFailureSum > 0"></i>
                </p>
            </div>
            <div class="attendee_list_container" v-if="isTitleActive === 1">
                <AttendeeList :list="attendeeArray" :cid="cid" :avatarSize="20" ></AttendeeList>
            </div>
            <div class="device_list" v-show="isTitleActive === 2">
                <div class="device_tool">
                    <el-button type="primary" size="mini" @click="gotoTvWall" v-if="isShowMonitorWall">{{
                        $t('tv_wall_text')
                    }}</el-button>
                    <el-button type="primary" size="mini" @click="deviceBinding" v-if="isWorkStation || isDrConnect">{{
                        $t('device_binding')
                    }}</el-button>
                </div>
                <vue-scroll>
                    <div class="device_item clearfix" v-for="device of deviceArray" :key="'device' + device.id">
                        <span>
                            <i class="icon iconfont iconmobilefill" v-if="device.device_type === 9"></i>
                            <i class="icon iconfont iconchaoshengbo" v-else-if="device.device_type === 6"></i>
                            <i class="icon iconfont iconbijiben" v-else></i>
                        </span>
                        <div class="device_item_name" @click="editDeviceDepartment(device)">
                            {{ device.device_name || device.device_id }}
                        </div>
                        <div class="device_error_number" v-if="deviceFailure[device.device_id]" >
                            <i class="icon iconfont iconjinggao"></i>
                            ({{deviceFailure[device.device_id]}})
                        </div>
                    </div>
                </vue-scroll>
            </div>
        </div>
        <CommonDialog
            :title="$t('modify_basic_info_text')"
            :show.sync="dialogVisible"
            :modal="false"
            :close-on-press-escape=true
            width="820px"
            class="machineDialog"
            >
            <el-form ref="device" :model="selectDevice" label-width="160px">
                <el-form-item :label="$t('machine_info_name')">
                    <el-input v-model="selectDevice.name"></el-input>
                </el-form-item>
                <el-form-item :label="$t('admin_hospital_name')">
                    <el-autocomplete
                        class="inline-input"
                        v-model="selectDevice.hospital_name"
                        :fetch-suggestions="queryHospital"
                        :placeholder="$t('input_enter_tips')"
                        :trigger-on-focus="false"
                        @select="selectHospital"
                    ></el-autocomplete>
                </el-form-item>
                <el-form-item :label="$t('statistic.using_departments')">
                    <el-autocomplete
                        class="inline-input"
                        v-model="selectDevice.using_department"
                        :fetch-suggestions="queryDepartment"
                        :placeholder="$t('input_enter_tips')"
                        :trigger-on-focus="false"
                    ></el-autocomplete>
                </el-form-item>
                <el-form-item :label="$t('statistic.owner_departments')">
                    <el-autocomplete
                        class="inline-input"
                        v-model="selectDevice.owner_department"
                        :fetch-suggestions="queryDepartment"
                        :placeholder="$t('input_enter_tips')"
                        :trigger-on-focus="false"
                    ></el-autocomplete>
                </el-form-item>
            </el-form>
            <div class="device_error_history">
                <p class="history_title">{{$t('historical_faults')}}</p>
                <div class="device_error_list" v-loading="loadingDeviceFailure">
                    <el-table
                        :data="deviceFailureList"
                        border
                        :row-class-name="getDeviceItemClass"
                        style="width: 100%">
                        <el-table-column
                          :label="$t('fault_time')">
                          <template slot-scope="{row}">
                            {{row.started_at | formatDate}}
                          </template>
                        </el-table-column>
                        <el-table-column
                          prop="alarm_code"
                          :label="$t('fault_code')">
                        </el-table-column>
                        <el-table-column
                          prop="alarm_detail.error_msg"
                          show-overflow-tooltip
                          :label="$t('fault_description')">
                        </el-table-column>
                        <el-table-column
                          :label="$t('status_text')">
                          <template slot-scope="{row}">
                            {{row.status | failureStatus}}
                          </template>
                        </el-table-column>
                        <el-table-column
                          :label="$t('operation')"
                          class-name="operate">
                            <template slot-scope="{row}">
                                <i v-if="row.status === 'NEW' && !disableEditDeviceDepartment" @click="closeDeviceFailure(row)" class="iconfont iconduihao-yuankuang"></i>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <el-pagination
                  v-show="deviceFailureList.length"
                  background
                  :current-page="queryForm.pageNo"
                  :layout="layout"
                  :page-size="queryForm.pageSize"
                  :total="deviceFailureTotal"
                  @current-change="handleCurrentChange"
                  @size-change="handleSizeChange"
                  style="margin-top:20px"
                />
            </div>
            <span slot="footer">
                <el-button @click="unBindingDeviceGroup" :disabled="disableEditDeviceDepartment">{{$t('statistic.unbind')}}</el-button>
                <el-button type="primary" @click="saveDeviceDepart" :disabled="disableEditDeviceDepartment">{{$t('statistic.confirm_btn')}}</el-button>
            </span>
        </CommonDialog>
    </div>
</template>
<script>
import {checkIsManagerOrCreator} from "../lib/common_base";
import base from "../lib/base";
import CommonDialog from "../MRComponents/commonDialog.vue"; //3rd change

import {
    parseImageListToLocal,
    openVisitingCard,
    patientDesensitization,
    getLocalAvatar,
    formatAttendeeNickname,
} from "../lib/common_base";
import GalleryFileList from "./galleryFileList.vue";
import GroupFavorite from "./groupFavorite.vue";
import Tool from "@/common/tool.js";
import service from "../service/service";
import AttendeeList from '../components/attendeeList.vue'
import moment from 'moment'
import i18n from '@/common/i18n'
export default {
    mixins: [base],
    name: "chatWindowRight",
    components: {
        GalleryFileList,
        GroupFavorite,
        AttendeeList,
        CommonDialog
    },
    props: {
        cid: [Number, String],
    },
    filters:{
        formatDate(ts){
            return moment(ts).format("YYYY-MM-DD HH:mm:ss");
        },
        failureStatus(status){
            if (status === 'NEW') {
                return i18n.t('unprocessed')
            }else if (status === 'RESOLVE') {
                return i18n.t('processed')
            }else{
                return ''
            }
        }
    },
    data() {
        return {
            getLocalAvatar,
            tab: 1,
            examFile: [],
            realtimeReview: [],
            isTitleActive: 1,
            allFileActiveTab: "all",
            selectDevice: {},
            dialogVisible: false,
            hospital_id: 0,
            hospital_name: '',
            deviceFailureList:[],
            layout: 'total, sizes, prev, pager, next, jumper',
            queryForm:{
                pageNo:1,
                pageSize:10,
            },
            deviceFailureTotal:0,
            loadingDeviceFailure:false,
        };
    },
    computed: {
        conversation() {

            return this.conversationList[this.cid] || {};

        },
        galleryObj() {
            return this.conversation.galleryObj || {};
        },
        allFileList() {
            var galleryList = this.galleryObj.gallery_list || [];
            return galleryList;
        },
        imageFileList() {
            var imageList = this.galleryObj.image_list || [];
            return imageList;
        },
        videoFileList() {
            var videoList = this.galleryObj.video_list || [];
            return videoList;
        },
        examFileList() {
            var examList = this.galleryObj.exam_list || [];
            return examList;
        },
        onlineAttendeeNum() {
            let num = 0;
            const list = this.attendeeArray;
            for (let i = 0; i < list.length; i++) {
                if (list[i].state == 1) {
                    num++;
                }
            }
            return num;
        },
        attendeeArray() {
            return formatAttendeeNickname(this.conversation.attendeeList);
        },
        deviceInfo() {
            return this.$store.state.device;
        },
        isDrConnect(){
            return this.deviceInfo.drConnectStatus && this.deviceInfo.DRSN;
        },
        isWorkStation() {
            return this.isCef && Tool.ifAppWorkstationClientType(this.systemConfig.clientType);
        },
        isShowMonitorWall() {
            return (
                this.$checkPermission({regionPermissionKey: 'tvwall'}) && this.user.role > 1 && this.deviceArray.length > 0 && !this.isWorkStation
            );
            // return this.user.enable_monitor_wall&&this.user.role>1
        },
        remarkMap() {
            return this.$store.state.friendList.remarkMap;
        },
        deviceArray() {
            let list = this.conversation.device_list || [];
            if (list.length === 0) {
                this.isTitleActive = 1;
            }
            return list;
        },
        // 群主,管理员,设备管理员才有权限解绑
        disableEditDeviceDepartment() {
            const isAdmin = checkIsManagerOrCreator(this.cid);
            const isOwner = this.selectDevice.owner_id === this.user.uid;
            return !isAdmin && !isOwner;
        },
        deviceFailure(){
            let filterDeviceFailure = {}
            const isAdmin = checkIsManagerOrCreator(this.cid);
            for(let device of this.deviceArray){
                if (isAdmin || device.owner_id === this.user.uid) {
                    filterDeviceFailure[device.device_id] = this.deviceInfo.deviceFailure[device.device_id]
                }
            }
            return filterDeviceFailure;
        },
        deviceFailureSum(){
            let sum = 0;
            for(let device_id in this.deviceFailure){
                if (this.deviceFailure[device_id]) {
                    sum += this.deviceFailure[device_id];
                }
            }
            return sum;
        }
    },
    watch: {
        cid: {
            handler(val) {
                if (Number(val)) {
                    this.getDeviceList(val);
                }
            },
            immediate: true,
        },
    },
    created() {},
    mounted() {
        this.$nextTick(() => {
            this.$root.eventBus.$off("loadMoreImage").$on("loadMoreImage", this.loadMore);
            this.$root.eventBus.$off("insertCollection").$on("insertCollection", this.insertCollection);
            this.$root.eventBus.$off("removeResourceItemFromCategory").$on("removeResourceItemFromCategory", (file) => {
                this.$refs.chatRightGroupFavorite.removeResourceItemFromCategory(file);
            });
            this.$root.eventBus.$off("refreshCollection").$on("refreshCollection", () => {
                this.$refs.chatRightGroupFavorite.refreshCollection();
            });
        });
    },
    methods: {
        getCommentNum(commentList = []) {
            let num = 0;
            commentList.forEach((element) => {
                if (element.status !== 0) {
                    if (!element.is_private || (element.is_private && this.user.uid === element.author_id)) {
                        num++;
                    }
                }
            });
            return num;
        },
        toggleTab(tab) {
            this.tab = tab;
        },
        loadMore(type = "all") {
            return new Promise((resolve, reject) => {
                let listDom, gallery_index_name, gallery_list_name, last_index;
                if (type === "all") {
                    last_index = this.galleryObj.gallery_index;
                    listDom = this.$refs.allFileList;
                    gallery_index_name = "gallery_index";
                    gallery_list_name = "gallery_list";
                } else if (type === "image") {
                    last_index = this.galleryObj.image_index;
                    listDom = this.$refs.imageFileList;
                    gallery_index_name = "image_index";
                    gallery_list_name = "image_list";
                } else if (type === "video") {
                    last_index = this.galleryObj.video_index;
                    listDom = this.$refs.videoFileList;
                    gallery_index_name = "video_index";
                    gallery_list_name = "video_list";
                } else if (type === "exam") {
                    last_index = this.galleryObj.exam_index;
                    listDom = this.$refs.examFileList;
                    gallery_index_name = "exam_index";
                    gallery_list_name = "exam_list";
                } else {
                    return;
                }
                if (last_index == -1) {
                    listDom && listDom.loadFinished();
                    return;
                }
                console.info("last_index", last_index);
                window.main_screen.conversation_list[this.cid].getResourceList(
                    {
                        limit: this.systemConfig.consultationImageShowNum,
                        type,
                        lastResourceID: last_index,
                    },
                    (res) => {
                        console.info(res, "getResourceList", type);
                        if (res.error_code === 0) {
                            listDom && listDom.loadFinished();
                            let data = {
                                cid: this.cid,
                            };
                            if (res.data.length > 0) {
                                data[gallery_list_name] = res.data;
                                data[gallery_index_name] = res.data[res.data.length - 1].resource_id;
                                patientDesensitization(data[gallery_list_name]);
                                parseImageListToLocal(data[gallery_list_name], "url");
                            } else {
                                data[gallery_index_name] = -1;
                            }

                            this.$store.commit("conversationList/pushMoreGroupFile", data);
                            resolve(data);
                        } else {
                            this.$message.error("getResourceList error 3");
                            reject("getResourceList error 3");
                        }
                    }
                );
            });
        },
        openVisitingCard(messages, type) {
            openVisitingCard(messages, type);
        },
        insertCollection(value) {
            if (String(value.file.group_id) === String(this.cid)) {
                let dom = this.$refs.chatRightGroupFavorite;
                dom.insertCollection(value);
            }
        },
        changeGroupAttendeeShow(index) {
            if (index == 1) {
                this.isTitleActive = 1;
            } else {
                this.isTitleActive = 2;
            }
        },
        gotoTvWall() {
            if (this.isCef) {
                this.$router.push(this.$route.fullPath + "/tv_wall_web");
            } else {
                this.$router.push(this.$route.fullPath + "/tv_wall_web");
            }
        },
        deviceBinding() {
            let deviceType = this.systemConfig.clientType;
            let device_id = this.deviceInfo.device_id
            let device_name = this.deviceInfo.device_name || this.deviceInfo.device_id
            if (this.isDrConnect) {
                deviceType = this.systemConfig.client_type.DR;
                device_id = this.deviceInfo.DRSN;
                device_name = this.deviceInfo.DRSN;
            }
            this.$confirm(
                `${this.$t('whether_pull_equipment_into_group')}(${device_name})`,
                this.$t('tip_title'),
                {
                    confirmButtonText: this.$t('confirm_button_text'),
                    cancelButtonText: this.$t('cancel_button_text'),
                }
            ).then(() => {
                window.main_screen.deviceBinding(
                    {
                        device_id,
                        group_id: this.cid,
                        device_name,
                        device_type: deviceType,
                    },
                    (res) => {
                        if (res.error_code == 0) {
                            this.$store.commit("conversationList/addDeviceToConversation", {
                                cid: this.cid,
                                device: res.data.deviceData,
                            });
                            this.$message.success(this.$t('device_binding_success'));
                        } else {
                            console.error(res);
                        }
                    }
                );
            });
        },
        async getDeviceList() {
            await Tool.handleAfterMainScreenCreated();
            window.main_screen.getDeviceList({ cid: this.cid }, (res) => {
                if (res.error_code == 0) {
                    this.$store.commit("conversationList/updateDeviceList", {
                        cid: this.cid,
                        list: res.data.list,
                    });
                }
            });
        },
        unBindingDeviceGroup() {
            this.$confirm(this.$t('whether_remove_equipment_from_group'), this.$t('tip_title'), {
                confirmButtonText: this.$t('confirm_button_text'),
                cancelButtonText: this.$t('cancel_button_text'),
                type: "warning",
            })
                .then(() => {
                    window.main_screen.deviceUnBinding({
                        id: this.selectDevice.id
                    },
                    (res) => {
                        if (res.error_code == 0) {
                            this.dialogVisible = false
                            this.$message.success(this.$t('operate_success'));
                            this.getDeviceList();
                        }
                    }
                    );
                })
                .catch(() => {});
        },
        handleClickTab(tab) {
            if (tab.name === "image") {
                if (this.imageFileList.length === 0) {
                    this.loadMore("image");
                }
            } else if (tab.name === "video") {
                if (this.videoFileList.length === 0) {
                    this.loadMore("video");
                }
            } else if (tab.name === "exam") {
                if (this.examFileList.length === 0) {
                    this.loadMore("exam");
                }
            }
        },
        openGallery(event, type) {
            let index = event.index;
            let file = event;
            let list = [];
            if (type === "all") {
                list = this.allFileList;
            } else if (type === "image") {
                list = this.imageFileList;
            } else if (type === "video") {
                list = this.videoFileList;
            } else if (type === "exam") {
                list = this.examFileList;
            }
            console.log(file);
            //打开画廊
            this.$store.commit("gallery/setGallery", {
                list,
                openFile: file,
                loadMore: true,
                loadMoreCallback: async () => {
                    await this.loadMore(type);
                    let list = [];
                    if (type === "all") {
                        list = this.allFileList;
                    } else if (type === "image") {
                        list = this.imageFileList;
                    } else if (type === "video") {
                        list = this.videoFileList;
                    } else if (type === "exam") {
                        list = this.examFileList;
                    }
                    this.$store.commit("gallery/setGallery", {
                        list,
                    });
                },
            });
            this.$nextTick(() => {
                this.$router.push(this.$route.fullPath + "/gallery");
            });
        },
        resetSelectDevice({id, device_id, device_name}) {
            this.selectDevice = {
                id,
                device_id,
                owner_id: 0,
                name: device_name ||"",
                hospital_id: 0,
                hospital_name: "",
                using_department: "",
                owner_department: ""
            }
        },
        editDeviceDepartment(device) {
            this.resetSelectDevice(device)
            this.dialogVisible = true
            window.main_screen.getDeviceDepartmentAndHospital({
                device_group_id: this.selectDevice.id
            }, (res) => {
                if (res.error_code == 0) {
                    const { usingDepartmentName, ownerDepartmentName, hospital, owner_id } = res.data;
                    this.selectDevice.hospital_name = hospital ? hospital.name : "";
                    this.hospital_id = hospital ? hospital.id : 0;
                    this.selectDevice.owner_id = owner_id;
                    this.selectDevice.using_department = usingDepartmentName;
                    this.selectDevice.owner_department = ownerDepartmentName;
                } else {
                    console.error(res);
                }
            });
            this.initDeviceFailureList();
        },
        async queryHospital(searchTxt, cb) {
            const res = await service.searchHospital({key: searchTxt});
            const hospitals = res.data.data.map((h) => {
                return {value: h.name, id: h.id}
            });
            if (!hospitals.length) {
                this.selectDevice.hospital_id = 0
            }
            cb(hospitals);
        },
        async queryDepartment(searchTxt, cb) {
            const res = await service.getDepartmentByName({
                name: searchTxt,
                page: 1,
                pagesize: 50,
            });
            const departments = res.data.data.list.map((d) => {
                return {value: d.name, id: d.id}
            });
            cb(departments);
        },
        selectHospital(hospitalInfo) {
            this.selectDevice.hospital_id = hospitalInfo.id
        },
        async saveDeviceDepart() {
            console.log('saveDeviceDepart',this.selectDevice)
            window.main_screen.deviceRename(this.selectDevice,
                (res) => {
                    if (res.error_code == 0) {
                        this.dialogVisible = false;
                        this.$message.success(this.$t('update_success_text'));
                        this.getDeviceList();
                    } else {
                        console.error(res);
                    }
                })
        },
        handleSizeChange(val) {
            this.queryForm.pageSize = val
            this.fetchData()
        },
        handleCurrentChange(val) {
            this.queryForm.pageNo = val
            this.fetchData()
        },
        fetchData(){
            this.loadingDeviceFailure = true;
            service.getAllDeviceFailure({
                series_numbers:[this.selectDevice.device_id],
                page_num:this.queryForm.pageNo,
                page_size:this.queryForm.pageSize,
            }).then(res=>{
                this.loadingDeviceFailure = false;
                if (res.data.error_code == 0) {
                    this.deviceFailureTotal = res.data.data.total;
                    this.deviceFailureList = res.data.data.alarm_list;
                }
            })
        },
        getDeviceItemClass({row}){
            if (row.status === 'NEW') {
                return 'error';
            }
            return ''
        },
        initDeviceFailureList(){
            this.queryForm.pageSize = 10;
            this.queryForm.pageNo = 1;
            this.deviceFailureList = [];
            this.fetchData()
        },
        closeDeviceFailure(item){
            service.resolveDeviceFailure({
                series_number:item.device_id,
                id:item._id
            }).then(res=>{
                if (res.data.error_code == 0) {
                    this.fetchData();
                }
            })
        }
    },
};
</script>
<style lang="scss">
.machineDialog{
    .el-dialog__body{
        display: flex;
        flex-direction: column;
        .device_error_history{
            flex:1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            .history_title{
                padding: 10px;
            }
            .device_error_list{
                flex: 1;
                overflow: auto;
                .error{
                    color: #f00;
                }
                .operate{
                    text-align: center;
                    .iconduihao-yuankuang{
                        color: #5c0;
                        cursor: pointer;
                    }
                }
            }
        }
    }
    .el-input {
        width: 80%;
    }
    .el-select {
        width: 100%;
    }
    .el-autocomplete {
        width: 100%;
    }
}
.chat_window_right {
    width: 320px;
    border-right: 1px solid #d9d9d9;
    display: flex;
    flex-direction: column;
    .tabs {
        height: 70px;
        border-bottom: 1px solid #dbdbdb;
        background-color: #e7eff2;
        display: flex;
        .tabItem {
            flex: 1;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            font-size: 14px;
            .iconfont {
                margin-bottom: 5px;
            }
            &.active {
                background: #fff;
                color: #00c59d;
            }
        }
        // .tab2{
        //     background:transparent url('../../../../static/resource_pc/images/c17-1.png') center -6px no-repeat;
        //     &:hover{
        //         background-image:url('../../../../static/resource_pc/images/c17-2.png');
        //     }
        //     &.active{
        //         background-image:url('../../../../static/resource_pc/images/c17-3.png');
        //     }
        // }
        // .tab3{
        //     background:transparent url('../../../../static/resource_pc/images/c19-1.png') center -6px no-repeat;
        //     &:hover{
        //         background-image:url('../../../../static/resource_pc/images/c19-2.png');
        //     }
        //     &.active{
        //         background-image:url('../../../../static/resource_pc/images/c19-3.png');
        //     }
        // }
        // .tab4{
        //     background:transparent url('../../../../static/resource_pc/images/c18-1.png') center -6px no-repeat;
        //     &:hover{
        //         background-image:url('../../../../static/resource_pc/images/c18-2.png');
        //     }
        //     &.active{
        //         background-image:url('../../../../static/resource_pc/images/c18-3.png');
        //     }
        // }
        .tab1 {
            // background:transparent url('../../../../static/resource_pc/images/c18-1.png') center -6px no-repeat;
            // &:hover{
            //     background-image:url('../../../../static/resource_pc/images/c18-2.png');
            // }
            // &.active{
            //     background-image:url('../../../../static/resource_pc/images/c18-3.png');
            // }
        }
    }
    .group_files {
        flex: 1;
        overflow: hidden;
        position: relative;
        overflow: hidden;
        background: #fff;
        .__view {
            height: 100%;
        }
        .all_files_container {
            height: 100%;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            padding: 10px;
            .el-tabs__header {
                flex-shrink: 1;
            }
            .el-tabs__content {
                overflow: auto;
                flex: 1;
                .el-tab-pane {
                    // height: 100%;
                }
            }
        }
    }
    .group_attendee {
        height: 260px;
        display: flex;
        flex-direction: column;
        cursor: auto;
        position: relative;
        &::before {
            content: "";
            position: absolute;
            top: -5px; /* 调整偏移量以适应需要 */
            left: 0;
            right: 0;
            height: 5px;
            cursor: n-resize; /* 鼠标悬停时的样式 */
        }
        .title_wrapper {
            display: flex;
            flex-wrap: nowrap;
            border-top: 1px solid #ebccd1;
            border-bottom: 1px solid #ebccd1;
            .title:first-child {
                border-right: 1px solid #ebccd1;
            }
            .title_active {
                background: #fff;
            }
            .title {
                flex: 1 1 auto;
                height: 42px;
                line-height: 42px;
                padding-left: 10px;

                .icongroups,.iconbijiben {
                    font-size: 25px;
                    color: #b9cacb;
                    float: left;
                }
                .iconbijiben {
                    font-size: 20px;
                }
                .iconjinggao{
                    color: #f00;
                }
                span {
                    line-height: 40px;
                    display: inline-block;
                    margin-left: 5px;
                }
            }
        }
        .attendee_list_container {
            flex: 1;
            overflow: hidden;
            padding: 5px 5px;
            height:300px;
        }
        .device_list {
            flex: 1;
            overflow: hidden;
            padding: 5px 10px;
            display: flex;
            flex-direction: column;
            .device_item {
                padding: 5px 0px;
                cursor: pointer;
                display: flex;
                align-items: center;
                .device_item_name{
                    padding-left: 6px;
                    flex:1;
                }
                .device_error_number{
                    color: #f00;
                    .iconjinggao{
                        color: #f00;
                    }
                }
                & > span {
                    background: #53b6f8;
                    border-radius: 50%;
                    display: inline-block;
                    width: 24px;
                    height: 24px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                }
                i {
                    font-size: 16px;
                    color: #fff;
                }
                & > p {
                    vertical-align: top;
                    margin-left: 6px;
                    max-width: 176px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    margin-right: 10px;
                }
                .iconmobilefill {
                    font-size: 20px;
                }
                .delete-button {
                    padding: 0;

                    i {
                        color: red;
                    }
                }
            }
        }
    }
}
</style>
