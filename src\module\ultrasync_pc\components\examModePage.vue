<template>
    <div class="exam_mode_page">
        <exam-search-bar ref="exam_search_bar" :showStatus="isShowMulticenterOperate"
            :isHFRMulticenter="isHFRMulticenter" :searchCallback="searchCallback"
            :changeCondition="changeCondition"></exam-search-bar>
        <div class="multicenter_count" v-if="isShowMulticenterStatus">
            <span>{{ $t('exam_status["1"]') }}：{{ multicenterStatusObj[1] }}</span>
            <span>{{ $t('exam_status["2"]') }}：{{ multicenterStatusObj[2] }}</span>
            <span>{{ $t('exam_status["6"]') }}：{{ multicenterStatusObj[6] }}</span>
            <span>{{ $t('exam_status["3"]') }}：{{ multicenterStatusObj[3] }}</span>
        </div>
        <div class="exam_right_tools">
            <el-popover placement="top" trigger="hover" popper-class="toolbar_item">
                <span>{{ $t('export_case') }}</span>
                <i class="icon iconfont icondownload toolbar_btn" :class="[isShowExportCheckbox ? 'active' : '']"
                    @click="openDownLoadExamModule" slot="reference"></i>
            </el-popover>
            <el-popover placement="top" trigger="hover" popper-class="toolbar_item">
                <span>{{ $t('create_new_exam') }}</span>
                <i class="icon iconfont iconplus toolbar_btn" @click="openNewExamModule" slot="reference"></i>
            </el-popover>
            <el-popover placement="top" trigger="hover" popper-class="toolbar_item">
                <span>{{ $t('normal_view_mode') }}</span>
                <i class="icon iconfont icongroupchat-icon toolbar_btn" @click="togglePageType" slot="reference"></i>
            </el-popover>
            <template v-if="isObstetricQCMulticenter && $checkPermission({regionPermissionKey: 'obstetricalAI'})">
                <el-popover placement="top" trigger="hover" popper-class="toolbar_item">
                    <span>{{ $t('obstetric_qc_multicenter') }}</span>
                    <i class="icon iconfont iconfenbushishujuku top_icon toolbar_btn" @click="goToMulticenter"
                        slot="reference"></i>
                </el-popover>
            </template>
            <template v-if="
                isDrAiAnalyze &&
                $checkPermission({regionPermissionKey: 'drAIAssistant'}) &&
                $store.state.device.isIStationInfoDR &&
                $store.state.device.drConnectStatus
            ">
                <el-popover placement="top" trigger="hover" popper-class="toolbar_item">
                    <span>{{ $t('dr_ai_analyze_statistics') }}</span>
                    <i class="icon iconfont iconshanxingzhanbi top_icon toolbar_btn" @click="goToDrAiAnalyzeStatistics"
                        slot="reference"></i>
                </el-popover>
            </template>
        </div>
        <div class="exam_list" v-loading="supplying">
            <el-checkbox v-model="isCheckAll" :label="$t('multi_select')" class="select_checkbox"
                @change="toggleCheckAll" v-if="examGroup.length > 0 && isShowExportCheckbox"></el-checkbox>
            <vue-scroll>
                <div v-for="(group, groupIndex) of examGroup" :key="'examGroup' + groupIndex" class="group_container">
                    <p class="group_title">{{ group.title }}</p>
                    <div v-for="(item, e_index) of group.list" class="exam_item_container" :key="'exam' + e_index">
                        <el-checkbox v-model="checkList" :label="item.exam_id" class="select_checkbox"
                            @change="handleSelectExam" v-if="isShowExportCheckbox">{{ "" }}</el-checkbox>
                        <div class="exam_item">
                            <div @click="toggleImages(item)" class="exam_title clearfix">
                                <div class="exam_uploader">
                                    <mr-avatar :url="getLocalAvatar(item.sender_nickname[0])" :radius="40"
                                        :showOnlineState="false" :key="item.sender_nickname[0].avatar"></mr-avatar>
                                    <div class="uploader_info">
                                        <div class="sender">
                                            <span v-for="(sender, senderIndex) of item.sender_nickname"
                                                :key="'sender' + senderIndex">
                                                {{
                                                    sender.hospital_name
                                                        ? sender.hospital_name + "-" + sender.nickname
                                                        : sender.nickname
                                                }}
                                            </span>
                                        </div>
                                        <div class="date">
                                            {{ $t('upload_date_text') }}{{ formatTime(item.upload_ts) }}
                                            <span v-if="
                                                isAiAnalyze &&
                                                (item.isIworksTest || checkIworksTest(item))
                                            " class="iWorks_test" v-permission="{regionPermissionKey: 'breastAI'}">
                                                {{ $t('cloud_statistics_iworks') }}</span>
                                        </div>
                                    </div>
                                </div>
                                <i v-show="!item.openState" class="icon iconfont icondown"></i>
                                <i v-show="item.openState" class="icon iconfont iconup"></i>
                                <i v-if="item.opinionList.length > 0" class="icon iconfont iconcomment">
                                    <span>{{ item.opinionList.length }}</span>
                                </i>
                            </div>
                            <div class="exam_patient">
                                <div class="field">{{ $t('patient_name') }}: {{item.patientInfo.patient_name}}</div>
                                <div class="field">{{ $t('exam_type') }}: {{ $t('exam_types')[item.exam_type] }}</div>
                                <div class="field">{{ $t('patient_age') }}: {{ item.patientInfo.patient_age }}</div>
                                <div class="field">{{ $t('patient_sex') }}: {{ item.patientInfo.patient_sex }}</div>
                                <template v-if="isHFRMulticenter">
                                    <div class="field" v-if="item.exam_custom_info && item.exam_custom_info.annotator">
                                        {{ $t('calibrater_text')
                                        }}{{ item.exam_custom_info && item.exam_custom_info.annotator }}
                                    </div>
                                    <div class="field" v-else>
                                        {{ $t('calibrater_text') }}{{ $t('uncalibrated_text') }}
                                    </div>
                                </template>
                                <div class="field">{{ $t('image_count') }}: {{ item.count }}</div>
                                <!-- ****检查状态**** -->
                                <div class="field" v-if="isShowMulticenterOperate">
                                    {{ $t('patient_case_status') }}{{ getItemStatusText(item.examStatus) }}
                                </div>
                                <!-- ****检查时间**** -->
                                <div class="field" :title="item.patient_series_datetime">
                                    {{ $t('exam_time') }}: {{ formatTime(item.patient_series_datetime) }}
                                </div>
                                <!-- ****iworks腹部学习**** -->
                                <template v-if="$checkPermission({regionPermissionKey: 'breastAI'}) && isAiAnalyze && checkIworksTest(item)">
                                    <div class="field">
                                        {{ $t('test_time_text')
                                        }}{{ getDateDiff(item.patient_series_datetime, item.upload_ts)
                                        }}<span class="icon iconfont iconwenhao hover_tips"
                                            :title="$t('test_time_tips')"></span>
                                    </div>
                                    <div class="field" v-if="item.exam_type && item.exam_type !== 2">
                                        {{ $t('exam_compliance_rate') }}:{{ iworksTestInfo(item).standard_rate }}
                                        <span
                                            class="icon iconfont qc_non_standard_icon icongantanhao-yuankuang click_tips"
                                            @click="openIworkTestSummary(item)">
                                        </span>
                                    </div>
                                    <div class="field"></div>
                                </template>
                                <template v-if="$checkPermission({regionPermissionKey: 'breastAI'}) && isAiAnalyze && isObstetricExamType(item)">
                                    <div class="field">
                                        {{ $t('exam_socre_text') }} {{ obstetricalTestInfo(item).score }}
                                    </div>
                                </template>
                                <!-- ****产科AI质控**** -->
                                <template v-else-if="
                                    $checkPermission({regionPermissionKey: 'obstetricalAI'}) &&
                                    isObstetricQCMulticenter //&&
                                    // item.mc_resource_list &&
                                    // item.mc_resource_list.is_analyzed
                                ">
                                    <div class="field" :title="$t('exam_socre_text')">
                                        {{ $t('exam_socre_text') }}
                                        {{
                                            item.mc_resource_list.list.length > 0 ? obstetricalTestInfo(item).score : 0
                                        }}
                                    </div>
                                </template>
                                <template v-if="isShowMulticenterOperate">
                                    <div v-if="item.examStatus == 3" class="refute_reason field"
                                        :title="item.reject_reason">
                                        {{ $t('refute_reason_text') }}{{ item.reject_reason }}
                                    </div>
                                </template>
                                <div class="patient_info clearfix iworks_test_summary_parent"
                                    v-if="$checkPermission({regionPermissionKey: 'breastAI'}) && isAiAnalyze && checkIworksTest(item)">

                                </div>
                            </div>
                            <div class="exam_image_list clearfix" v-show="item.openState">
                                <div class="exam_title">
                                    <div v-if="item.iworks_protocol_execution" class="iworks_info clearfix">
                                        <div class="icon_title">
                                            <i class="iconfont iconquanbuwenjian-weixuanzhong" id="iworks"></i>
                                        </div>
                                        <p>
                                            iWorks：{{ getIworksName(item) }} （{{
                                                item.uploaded_view_number
                                            }}/{{ item.all_view_number }}）
                                            <!-- <el-popover
                                                placement="right"
                                                popper-class="iworks_tree_popper"
                                                trigger="click">
                                                <el-tree
                                                :data="iworksProtocolTree"
                                                :default-expand-all="true"
                                                show-checkbox
                                                node-key="id"
                                                :ref="'protocol_tree_'+item.exam_id"
                                                ></el-tree>
                                                <i @click="showProtocol(item)" slot="reference" class="iconfont iconwarning-o"></i>
                                            </el-popover> -->
                                        </p>
                                    </div>
                                </div>
                                <div class="list clearfix" v-loading="item.imageState == 1">
                                    <template v-for="img of item.showImageList">
                                        <div class="file_item" @click.stop="clickGallery($event, img, 2, item)"
                                            @contextmenu.stop="
                                                callImageMenu($event, img, 'chatComponent_examImageItem')
                                                " :class="{ is_create: img.isCreate, is_delete: img.isDelete }"
                                            :key="img.resource_id" v-if="getResourceTempState(img.resource_id) === 1">
                                            <v-touch>
                                                <template v-if="img.url_local">
                                                    <p v-if="img.protocol_view_name" class="view_name">
                                                        {{ img.protocol_view_name }}
                                                    </p>
                                                    <img :src="img.error_image || img.url_local" class="file_image"
                                                        @error="setErrorImage(img)" />
                                                    <span v-if="img.count > 1" class="view_count">{{ img.count }}{{
                                                        $t('piece_tip') }}</span>
                                                    <span v-show="showAiAnalyzeIcon(img)">
                                                        <span v-for="(iconObj, index) in imageStandardIcon(img)"
                                                            :key="index" :class="[iconObj.css]" :title="iconObj.tips">
                                                            {{ iconObj.label }}
                                                        </span>
                                                    </span>
                                                </template>
                                                <template v-else>
                                                    <p class="view_name">{{ img.protocol_view_name }}</p>
                                                    <img src="static/resource_pc/images/default.png"
                                                        class="file_image" />
                                                    <span v-if="
                                                        $checkPermission({regionPermissionKey: 'breastAI'}) &&
                                                        isAiAnalyze &&
                                                        (item.isIworksTest || checkIworksTest(item))
                                                    ">
                                                        <span
                                                            class="icon iconfont ai_result_deletion_icon iconwenhao-yuankuang-copy"
                                                            :title="$t('view_deletion')">
                                                        </span>
                                                    </span>
                                                </template>

                                                <!-- <div v-else class="empty_view"></div> -->
                                                <i v-if="img.msg_type == systemConfig.msg_type.Cine"
                                                    class="icon iconfont iconvideo_fill_light"></i>
                                            </v-touch>
                                        </div>
                                    </template>

                                    <div class="file_item" @click.stop="ifNeedAiSelectDialog(item)">
                                        <i class="icon iconfont iconplus"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="exam_image_tool">
                                <el-button v-show="item.openState" @click="exportExam(item)" type="primary">{{
                                    $t('export_case')
                                }}</el-button>
                                <el-button v-show="item.openState && checkDeleteCaseAuth(item)"
                                    @click="handleDeleteExam(item)" type="primary">{{ $t('delete_case') }}</el-button>
                                <template v-if="isShowMulticenterOperate">
                                    <template v-if="
                                        isNormalMember &&
                                        item.examStatus !== systemConfig.multiCenter.exam_status.judge
                                    ">
                                        <el-button v-show="item.openState" @click="openSupplyCaseDialog(item)"
                                            type="primary">{{ $t('supply_case_title') }}</el-button>
                                    </template>
                                    <template v-else>
                                        <el-button v-show="item.openState" @click="openSupplyCaseDialog(item, true)"
                                            type="primary">{{ $t('watch_supply_case_title') }}</el-button>
                                    </template>
                                </template>
                            </div>

                            <div class="exam_comments" v-show="item.openState">
                                <div class="comment_editor">
                                    <p>{{ $t('exam_opinion_text') }}</p>
                                    <textarea v-model="item.examCommentText"></textarea>
                                    <span class="add_exam_comment" @click="addExamComment(item)">{{
                                        $t('add_btn')
                                    }}</span>
                                </div>
                                <div class="exam_comment_list">
                                    <div v-for="(comment, commentIndex) in item.opinionList"
                                        class="comment_item clearfix" :key="'common' + commentIndex">
                                        <div class="author_info fl">
                                            <p class="author">
                                                {{
                                                    attendeeList &&
                                                    attendeeList["attendee_" + comment.sender_id].nickname
                                                }}：
                                            </p>
                                        </div>
                                        <p @contextmenu.stop="callTextMenu($event, comment.content, 1)">
                                            {{ comment.content
                                            }}<span v-if="comment.type == 1">{{ $t('exam_conclusion_text') }}</span>
                                        </p>
                                        <div class="author_info_time">
                                            <p>
                                                <span>{{ formatTime(comment.send_ts) }}</span>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- <input
                    type="file"
                    :key="supply_exam_image_input_file_tag"
                    class="supply_exam_image_input_file"
                    @change="supplyImage"
                    ref="supply_exam_image"
                    accept=".jpg,.jpeg,.png,.pdf,.mp4,.dcm,.ai,.json"
                    multiple
                /> -->
                <!-- <el-upload
                    v-show="false"
                    action=""
                    list-type="picture-card"
                    :auto-upload="true"
                    :before-upload="handleFilesSelected"
                    :file-list="fileList"
                    :multiple="true"
                    :on-exceed="()=>{$message.error($t('supply_exam_image.err_tip.select_up_to_500_files'));}"
                    ref="upload"
                    :limit="500"
                    accept=".jpg,.jpeg,.png,.pdf,.mp4,.dcm,.ai,.json"
                    >
                </el-upload> -->
                <input ref="upload" v-show="false" multiple="multiple" type="file" class="select_picture"
                    accept=".jpg,.jpeg,.png,.pdf,.mp4,.dcm,.ai,.json" @change="handleFilesSelected()" />
                <p class="tip" v-show="index != -1" @click="loadmore" v-loading="loadingMore">
                    {{ $t('click_more_text') }}
                </p>
                <p class="tip" v-show="loadFail" @click="search">{{ $t('loading_fail') }}</p>
                <p class="tip" v-show="index == -1">{{ $t('no_more_data') }}</p>
            </vue-scroll>
        </div>
        <iworks-test-exam-report-dialog @closeiworksTestExamReportDialog="isShowiworksTestExamReportDialog = false"
            @clickGallery="clickGallery" :isShowiworksTestExamReportDialog="isShowiworksTestExamReportDialog"
            :exam="currentExam"></iworks-test-exam-report-dialog>
        <supply-case v-if="belongingMulticenter && belongingMulticenter.type == multicenterConfig.name.hfr_multicenter"
            ref="supplyCase"></supply-case>
        <thyroid-supply-case
            v-if="belongingMulticenter && belongingMulticenter.type == multicenterConfig.name.thyroid_multicenter"
            ref="thyroidSupplyCase"></thyroid-supply-case>
        <myocardial-supply-case v-if="
            belongingMulticenter &&
            belongingMulticenter.type == multicenterConfig.name.myocardial_strain_multicenter
        " ref="myocardialSupplyCase"></myocardial-supply-case>
        <generic-supply-case
            v-if="belongingMulticenter && belongingMulticenter.type == multicenterConfig.name.generic_multicenter"
            ref="genericSupplyCase"></generic-supply-case>
        <new-exam-form :show.sync="isShowNewExamPage" v-if="isShowNewExamPage"
            :isObstetricMc="isObstetricQCMulticenter"></new-exam-form>
        <div v-loading.fullscreen="dataPreparing" :element-loading-text="$t('data_preparing')"></div>
    </div>
</template>
<script>
import base from "../lib/base";
import Tool from "@/common/tool.js";
import sendMessage from "../lib/sendMessage";
import examListCommon from "../lib/examListCommon";
import iworksTool from "../lib/iworksTool";
import multiCenterService from "../service/multiCenterService";
import examSearchBar from "./examSearchBar";
import supplyCase from "./HFRMulticenter/supplyCase";
import thyroidSupplyCase from "./thyroidMulticenter/supplyCase";
import myocardialSupplyCase from "./myocardialMulticenter/supplyCase";
import genericSupplyCase from "./genericMulticenter/supplyCase";
import NewExamForm from "./newExamForm";
import iworksTestExamReportDialog from "./aiReport/iworksTestExamReportDialog.vue";
import obstetricTool from "../lib/obstetricTool";
import {
    parseImageListToLocal,
    transferPatientInfo,
    findProtocolViewNode,
    patientDesensitization,
    getItemStatusText,
    getLocalAvatar,
    getDateDiff,
    imageStandardIcon,
    checkIworksTest,
    toFixedNumber,
    hasAiAnalyzeResult,
    getResourceTempState,
    setIworksInfoToMsg,
    getAiViewsWithExamType,
} from "../lib/common_base";
import { cloneDeep } from "lodash";
import moment from "moment";
import { checkIsCreator, checkIsManager } from "../lib/common_base";
import uploadConsultImage from "../lib/uploadConsultImage";
export default {
    mixins: [base, sendMessage, iworksTool, examListCommon, obstetricTool, uploadConsultImage],
    name: "ExamModePage",
    permission: true,
    components: {
        examSearchBar,
        supplyCase,
        thyroidSupplyCase,
        myocardialSupplyCase,
        genericSupplyCase,
        NewExamForm,
        iworksTestExamReportDialog,
    },
    data() {
        return {
            setIworksInfoToMsg,
            getResourceTempState,
            getLocalAvatar,
            toFixedNumber,
            hasAiAnalyzeResult,
            imageStandardIcon,
            getDateDiff,
            checkIworksTest,
            getAiViewsWithExamType,
            cid: this.$route.params.cid,
            loadingMore: false,
            loadFail: false,
            iworksProtocolTree: [],
            examGroup: [],
            condition: {},
            belongingMulticenter: null,
            isNormalMember: true,
            supplying: false,
            supply_exam_image_input_file_tag: 0,
            isShowNewExamPage: false,
            isShowiworksTestExamReportDialog: false,
            multicenter_list: this.$store.state.multicenter.list,
            multicenter_config: this.$store.state.multicenter.config,
            multicenter_types: this.$store.state.multicenter.type,
            currentSuplyExam: null,
            isCheckAll: false,
            checkList: [],
            dataPreparing: false,
            isShowExportCheckbox: false,
            currentExam: null,
            isCreator: false,
            isManager: false,
            multicenterStatusObj: {
                1: 0,
                2: 0,
                3: 0,
                6: 0,
            },
            uploadTask:{}
        };
    },
    computed: {
        examObj() {
            return this.$store.state.examList[this.cid] || {};
        },
        examList() {
            return this.examObj.list || [];
        },
        isShowMulticenterOperate() {
            return (
                this.belongingMulticenter &&
                (this.belongingMulticenter.type == this.multicenterConfig.name.hfr_multicenter ||
                    this.belongingMulticenter.type == this.multicenterConfig.name.thyroid_multicenter ||
                    this.belongingMulticenter.type == this.multicenterConfig.name.myocardial_strain_multicenter ||
                    this.belongingMulticenter.type == this.multicenterConfig.name.generic_multicenter)
            );
        },
        isShowMulticenterStatus() {
            return (
                this.belongingMulticenter &&
                (this.belongingMulticenter.type == this.multicenterConfig.name.generic_multicenter ||
                    this.belongingMulticenter.type == this.multicenterConfig.name.myocardial_strain_multicenter)
            );
        },
        // examGroup(){
        //     let type=this.groupSelected;
        //     return this.parseExamListToGroup(this.examList);
        // },
        index() {
            return this.examObj.index || 0;
        },
        attendeeList() {
            return this.conversationList[this.cid].attendeeList;
        },
        multicenterConfig() {
            return this.$store.state.multicenter;
        },
        conversation() {
            this.isCreator = checkIsCreator(this.cid);
            this.isManager = checkIsManager(this.cid);
            return this.conversationList[this.cid] || {};
        },
        isObstetricQCMulticenter() {
            return this.conversation.multicenter_type == "obstetric_qc_multicenter";
        },
        isAiAnalyze() {
            return this.conversation.service_type == this.systemConfig.ServiceConfig.type.AiAnalyze;
        },
        mc_options() {
            return this.$store.state.multicenter.optionList[
                this.$store.state.multicenter.obstetricEarlyPregnancy.mcOpId
            ];
        },
        examListLength() {
            let examListLength = 0;
            this.examGroup.forEach((group) => {
                group.list.forEach((exam) => {
                    examListLength++;
                });
            });
            return examListLength;
        },
        isDrAiAnalyze() {
            return this.conversation.service_type == this.systemConfig.ServiceConfig.type.DrAiAnalyze;
        },
        isHFRMulticenter() {
            return this.conversation.multicenter_type == "hfr_multicenter";
        },
    },
    watch: {
        examListLength: {
            handler(val) {
                if (this.checkList.length == val) {
                    this.isCheckAll = true;
                } else {
                    this.isCheckAll = false;
                }
            },
        },
    },
    mounted() {
        this.$nextTick(() => {
            this.getExamList();
            // this.$store.commit('examList/initExamObj',this.cid);
            this.$root.eventBus
                .$off("updateExamImageListIfNeed")
                .$on("updateExamImageListIfNeed", this.updateExamImageListIfNeed);
            this.$root.eventBus.$off("deleteFileToExamList").$on("deleteFileToExamList", this.deleteFileToExamList);
            this.$root.eventBus.$off("deleteExamItem").$on("deleteExamItem", this.deleteExamItem);
            this.$root.eventBus
                .$off("refreshMulticenterCount")
                .$on("refreshMulticenterCount", this.refreshMulticenterCount);
        });
    },
    methods: {
        getIworksName(item){
            let protocol_name = ''
            if(item.iworks_protocol_execution){
                protocol_name = item.iworks_protocol_execution.protocol_name
            }
            if(!protocol_name && item.protocol_execution_guid){
                let iworks_protocol_execution = this.gallery.iworks_protocol_list[item.protocol_execution_guid];
                protocol_name = iworks_protocol_execution ? iworks_protocol_execution.protocol_name : ''
            }
            return protocol_name||''
        },
        showAiAnalyzeIcon(img) {
            return (
                (this.$checkPermission({regionPermissionKey: 'breastAI'}) && this.isAiAnalyze) ||
                (this.isDrAiAnalyze && this.$checkPermission({regionPermissionKey: 'drAIAssistant'})) ||
                (this.$checkPermission({regionPermissionKey: 'obstetricalAI'}) && img.mc_resource_map) ||
                true
            );
        },
        isFinshedAiAnalyze(item) {
            if (item.ai_analyze_list) {
                return (item.ai_analyze_list || []).reduce((h, v) => {
                    return h && v.status;
                }, 1);
            } else {
                return true;
            }
        },
        goToDrAiAnalyzeStatistics() {
            this.$router.push(this.$route.fullPath + "/dr_ai_statistics");
        },
        obstetricalTestInfo(exam) {
            if (this.$checkPermission({regionPermissionKey: 'obstetricalAI'}) && this.isObstetricQCMulticenter) {
                let newExam = this.dealExamListWithGroupView(
                    [{ ...exam, ...{ mc_resource_map: exam.mc_resource_list.list } }],
                    this.mc_options
                );
                let examInfo = this.dealExamViewInfo(newExam[0], this.mc_options);
                return examInfo;
            } else {
                if (exam.ai_analyze_list && exam.ai_analyze_list.length > 0) {
                    let list = exam.ai_analyze_list.reduce((h, v) => {
                        if (v.report && v.report.clips && Object.keys(v.report.clips).length > 0) {
                            let m = this.getMcResourceMap(
                                { ai_analyze_report: v, resource_id: Object.keys(v.report.clips)[0] },
                                true
                            );
                            h.push(m);
                        }
                        return h;
                    }, []);
                    let newExam = this.dealExamListWithGroupView(
                        [{ ...exam, ...{ mc_resource_map: list, image_num: list.length } }],
                        this.mc_options
                    );
                    let examInfo = this.dealExamViewInfo(newExam[0], this.mc_options);

                    return examInfo;
                }
            }
            return { score: "" };
        },
        iworksTestInfo(exam) {
            let views = getAiViewsWithExamType(exam)
            let result = cloneDeep(views);
            let desc_str = this.$t('iworks_clip_summary_text');
            let total = 0;
            let non_standard = 0;
            let standard = 0;
            if (exam.ai_analyze_list && exam.ai_analyze_list.length) {
                for (let item of exam.ai_analyze_list) {
                    let clips = (item.report && item.report.clips) || item.clips;
                    if (clips) {
                        for (let resource_id in clips) {
                            let clip = clips[resource_id][0];
                            if (clip.quality >= 2) {
                                non_standard += 1;
                            }
                            if ("type" in clip && result[clip.type] && clip.score && clip.id in result) {
                                result[clip.id].score    = result[clip.id].score||0
                                if(clip.score > result[clip.id].score ){
                                    result[clip.id].score = clip.score
                                    result[clip.id]['quality'] = clip.quality
                                    result[clip.id]['is_exist'] = true
                                }
                            }
                        }
                    }
                }
            }

            const guid = exam.protocol_execution_guid;
            const protocolInfo = this.gallery.iworks_protocol_list[guid];
            let rate = 0;
            if (protocolInfo && protocolInfo.child_nodes && protocolInfo.child_nodes.length > 0) {
                rate = 1 / protocolInfo.child_nodes.length;
            }
            if(rate==0){
                const protocolInfo = exam.iworks_protocol_execution
                if (protocolInfo && protocolInfo.child_nodes && protocolInfo.child_nodes.length > 0) {
                    rate = 1 / protocolInfo.child_nodes.length;
                }
            }
            let score = 0;
            let standard_rate = 0
            for (let id in result) {
                if (result[id].key.toLowerCase() != "undefined" && result[id].score) {
                    score += result[id].score * rate;
                }
                if(result[id].is_exist && result[id].quality <2){
                    standard +=1
                }
            }
            let resource_guids = [];
            for (let resource_id in exam.protocol_resource) {
                let protocol_resource = exam.protocol_resource[resource_id];
                if (protocol_resource.protocol_guid == exam.protocol_execution_guid) {
                    if (resource_guids.indexOf(protocol_resource.protocol_view_guid) < 0) {
                        resource_guids.push(protocol_resource.protocol_view_guid);
                    }
                }
            }
            if (exam.iworks_protocol_execution) {
                desc_str = desc_str.replace("${name}", exam.iworks_protocol_execution.attributes.name);
                desc_str = desc_str.replace("${total}", exam.iworks_protocol_execution.child_nodes.length);
                desc_str = desc_str.replace("${upload_num}", resource_guids.length);
                if (exam.iworks_protocol_execution.child_nodes.length>0) {
                    total = exam.iworks_protocol_execution.child_nodes.length
                    desc_str = desc_str.replace(
                        "${complete_rate}",
                        this.toFixedNumber((resource_guids.length / total) * 100) +
                            "%"
                    );
                } else {
                    desc_str = desc_str.replace("${complete_rate}", "0");
                }
                if (total>0) {
                    standard_rate =  this.toFixedNumber((standard / total) * 100) + "%";
                    desc_str = desc_str.replace(
                        "${standard_rate}",
                        standard_rate
                    );
                } else {
                    desc_str = desc_str.replace("${standard_rate}", "0");
                }
            }
            // console.error(desc_str)
            // desc_str = desc_str.replace('${total}',exam.protocol_execution_guid.child_nodes.length)
            // "本次检查遵循${name} 需完成${total}个切面打图，有效上传切面数：${upload_num}个，完成度：${complete_rate}。有效上传切面中，切面标准率：${standard_rate}。",
            return { score: this.toFixedNumber(score), desc_str: desc_str,standard_rate,total ,non_standard};
        },
        goToMulticenter() {
            if (!this.conversation || !this.conversation.multicenter_info) {
                return;
            }
            let item = this.conversation.multicenter_info;

            let type = this.multicenter_types[item.type];
            let config = this.multicenter_config[item.type];
            let route = config.roleRoute[item.userInfo.role];
            this.title = this.$t(type);
            let option = { cid: this.cid, fid: 0 };
            let conversation = this.conversationList[this.cid];
            if (conversation && conversation.is_single_chat) {
                option.fid = conversation.fid || 0;
            }
            this.$store.commit("multicenter/setCurrentConfig", config);
            this.$store.commit("multicenter/setCurrentMulticenter", item);
            this.$store.commit("multicenter/setAnonymous", item);
            this.$store.commit("multicenter/updateEnterByGroup", option);
            let url = `/main/index/chat_window/${this.cid}/multicenter`;
            this.$router.push(url);
        },
        async init() {
            this.cid = this.$route.params.cid;
            await this.findMulticenter();
            this.$refs.exam_search_bar.init();
            if (!this.examObj.init) {
                this.search();
            } else {
                //检查列表初始化过再次进入只渲染上次搜索条件
                this.$refs.exam_search_bar.renderSearchCondition(this.examObj);
                // this.parseExamListToGroup(this.examList);
            }
        },
        async viewReject() {
            this.$store.commit("examList/initExamObj", this.cid);
            await this.findMulticenter();
            this.$refs.exam_search_bar.init();
            this.$refs.exam_search_bar.renderSearchCondition({ mcStatus: 3 });
            this.$refs.exam_search_bar.search();
        },
        search() {
            this.$refs.exam_search_bar.search();
            this.getMulticenterStatus();
        },
        searchCallback(condition) {
            this.examGroup = [];
            this.condition = condition;
            this.$store.commit("examList/initExamObj", this.cid);
            this.$nextTick(() => {
                this.getExamList();
            });
            this.getMulticenterStatus();
        },
        togglePageType() {
            this.$root.eventBus.$emit("togglePageType");
            this.clearUploadTask();
            this.cancelDownLoadSelected();
        },
        getExamList(callback) {
            var that = this;
            if (this.loadingMore) {
                return;
            }
            // this.examList=[];
            this.loadingMore = true;
            this.loadFail = false;
            let index = this.examObj.index;
            this.controller = this.conversationList[this.cid].socket;

            let sort_by = this.condition.sortSelected == 1 ? "exam_date" : "upload_ts";
            let keys = this.condition;
            if (this.isShowMulticenterOperate) {
                keys.mcID = this.belongingMulticenter.id;
            }
            this.$store.commit("examList/updateExamObj", {
                cid: this.cid,
                keys: keys,
            });
            this.controller.emit(
                "get_exam_list",
                {
                    key: keys,
                    // start:index,
                    count: 20,
                    sort_by: sort_by,
                    skip: this.examList.length,
                },
                function (is_succ, data) {
                    console.log(data);
                    if (is_succ) {
                        patientDesensitization(data.exam_list);
                        for (let item of data.exam_list) {
                            item.patientInfo = transferPatientInfo(item);
                            item.imageState = 0;
                            item.openState = false;
                            item.imageList = [];
                            item.examStatus = 0;
                            item.iworksImages = [];
                            item.noneIworksImages = [];
                            item.examCommentText = "";
                            parseImageListToLocal(item.sender_nickname, "avatar");
                            if (item.iworks_protocol) {
                                that.setIworksProtocol(cloneDeep(item.iworks_protocol));
                            }
                            if (item.iworks_protocol_execution) {
                                that.setIworksProtocol(cloneDeep(item.iworks_protocol_execution));
                            }
                            // that.setDefaultImg(item.sender_nickname);
                        }
                        that.$store.commit("examList/setExamList", {
                            cid: that.cid,
                            exam_list: data.exam_list,
                            index: data.index,
                        });
                        that.getExamStatus(data.exam_list);
                        that.parseExamListToGroup();
                        if (that.loadingMore) {
                            that.loadingMore = false;
                        }

                        callback && callback();
                    } else {
                        that.loadFail = true;
                        that.loadingMore = false;
                        // that.$message.error(that.$t(data))
                    }
                }
            );
        },
        loadmore() {
            this.getExamList();
        },
        async toggleImages(exam) {
            var that = this;
            var item = exam;
            this.$store.commit("examList/updateExamById", {
                cid: this.cid,
                exam_id: exam.exam_id,
                key: {
                    openState: !item.openState,
                },
            });
            //切换展示状态
            if (item.imageState == 0) {
                //未下载过则下载图像列表
                try {
                    await this.loadImageList(exam);
                } catch (error) {
                    this.$message.error(error);
                }
            }
        },
        loadImageList(exam) {
            return new Promise((resolve, reject) => {
                let timer = null;
                this.$store.commit("examList/updateExamById", {
                    cid: this.cid,
                    exam_id: exam.exam_id,
                    key: {
                        imageState: 1,
                    },
                });
                this.controller = this.conversationList[this.cid].socket;
                var tag = this.condition.tag || [];
                this.controller.emit(
                    "get_exam_image_list",
                    {
                        exam_id: exam.exam_id,
                        key: {
                            tag: tag,
                        },
                    },
                    (is_succ, data) => {
                        if (is_succ) {
                            this.setShowImageList(exam, data.image_list);
                            resolve(true);
                        } else {
                            let key = {
                                imageState: 0,
                                openState: false,
                            };
                            this.$store.commit("examList/updateExamById", {
                                cid: this.cid,
                                exam_id: exam.exam_id,
                                key: key,
                            });
                            reject(this.$t('operate_err'));
                        }
                        clearTimeout(timer);
                        timer = null;
                    }
                );
                timer = setTimeout(() => {
                    let key = {
                        imageState: 0,
                        openState: false,
                    };
                    this.$store.commit("examList/updateExamById", {
                        cid: this.cid,
                        exam_id: exam.exam_id,
                        key: key,
                    });
                    reject("loadImageList timeout");
                }, 10000);
            });
        },
        extendIworksSameNodeImage(list) {
            let new_list = list.reduce((h, v) => {
                h = [...h, ...(v.same_node_image || []), v];
                return h;
            }, []);
            return new_list;
        },
        setShowImageList(exam, imageList, upload_ts) {
            for (let i in imageList) {
                let image = imageList[i];
                let commentObj = {};
                commentObj = {
                    ai_analyze_report: image.ai_analyze_report || {},
                    tag_names: image.tag_names,
                    comment_list: image.comment_list || [],
                    tags_list: image.tags_list || [],
                    showAISearchSuggest: image.showAISearchSuggest || false,
                };
                this.$store.commit("gallery/updateCommentToGallery", {
                    obj: commentObj,
                    resource_id: image.resource_id,
                });
            }
            let key = {};
            key.imageState = 2;
            patientDesensitization(imageList);
            parseImageListToLocal(imageList, "url");
            if (exam.iworks_protocol) {
                key.iworks_protocol = exam.iworks_protocol;
            }
            if (exam.iworks_protocol_execution) {
                key.iworks_protocol_execution = exam.iworks_protocol_execution;
            }
            for (let item of imageList) {
                setIworksInfoToMsg(item, exam.protocol_execution_guid);
            }
            //对检查图像进行去重
            key.imageList = this.deDuplicatingImg(imageList);
            this.parseIworksImage(key, exam.protocol_execution_guid);
            key.showImageList = key.iworksImages.concat(key.noneIworksImages);
            key.count = key.imageList.length;
            if (upload_ts) {
                key.upload_ts = upload_ts;
            }
            this.$store.commit("examList/updateExamById", {
                cid: this.cid,
                exam_id: exam.exam_id,
                key: key,
            });

            return key;
        },
        addExamComment(exam) {
            const exam_id = exam.exam_id;
            let content = exam.examCommentText;
            content = content.trim();
            if (content == "") {
                return;
            }
            this.controller = this.conversationList[this.cid].socket;
            this.controller.emit(
                "submit_exam_consultation_opinion",
                {
                    exam_id: exam_id,
                    content: content,
                },
                (is_succ, data) => {
                    exam.examCommentText = "";
                    if (is_succ) {
                        this.$store.commit("examList/addExamComment", {
                            cid: this.cid,
                            exam_id: exam_id,
                            comment: data.opinionList[0],
                        });
                    } else {
                        this.$message.error(this.$t(data));
                    }
                }
            );
        },

        async updateExamImageListIfNeed(msg) {
            //收到消息后实时刷新检查列表
            if (msg.msg_type == this.systemConfig.msg_type.EXAM_IMAGES) {
                msg = cloneDeep(msg);
                msg.msg_type = msg.cover_msg_type;
            }
            if (
                msg.msg_type == this.systemConfig.msg_type.OBAI ||
                msg.msg_type == this.systemConfig.msg_type.Frame ||
                msg.msg_type == this.systemConfig.msg_type.Cine
            ) {
                let hasExam = false;
                let cid = msg.group_id;
                let examObj = this.$store.state.examList[cid];
                if (!examObj) {
                    //检查列表没获取过，直接忽略
                    return;
                }
                let examList = examObj.list;
                for (let index = 0; index < examList.length; index++) {
                    let exam = examList[index];
                    if (msg.exam_id == exam.exam_id) {
                        hasExam = true;
                        //收到的会诊文件exam_id已在列表内
                        if (this.cid == cid) {
                            if (exam.imageState == 2) {
                                let obj = {};
                                if (exam.protocol_execution_guid) {
                                    setIworksInfoToMsg(msg, exam.protocol_execution_guid);
                                }
                                exam.imageList.unshift(msg);
                                if (msg.iworks_protocol) {
                                    exam.iworks_protocol = cloneDeep(msg.iworks_protocol);
                                }
                                if (msg.iworks_protocol_execution) {
                                    exam.iworks_protocol_execution = cloneDeep(msg.iworks_protocol_execution);
                                }
                                this.setShowImageList(exam, exam.imageList, msg.sender_nickname.upload_ts);
                                this.addUploaderIfneed(exam, msg, index);
                                this.updatePatientInfo(exam, msg);
                            } else if (exam.imageState === 0) {
                                // 没加载过，新增图片自动加载，以对齐count字段
                                try {
                                    this.updatePatientInfo(exam, msg);
                                    await this.loadImageList(exam);
                                } catch (error) {
                                    this.$message.error(error);
                                }
                            }
                            if (
                                this.belongingMulticenter && this.belongingMulticenter.type ==
                                this.multicenterConfig.name.myocardial_strain_multicenter
                            ) {
                                // 心肌应变多中心更新测量文件列表
                                this.updateExamInfo(exam.exam_id);
                            }
                        } else {
                            this.$store.commit("examList/initExamObj", cid);
                        }
                    }
                }
                if (!hasExam) {
                    //收到的会诊文件exam_id不在列表内,新增一个检查
                    if (examObj.init) {
                        if (this.cid == cid) {
                            this.search();
                        } else {
                            this.$store.commit("examList/initExamObj", cid);
                        }
                    }
                }
            }
        },
        addUploaderIfneed(exam, msg, index) {
            let senderList = exam.sender_nickname;
            let sender = msg.sender_nickname;
            let hasSender = false;
            for (let temp of senderList) {
                if (temp.sender_id == sender.sender_id) {
                    hasSender = true;
                }
            }
            if (!hasSender) {
                this.$store.commit("examList/updateExamByIndex", {
                    cid: this.cid,
                    index: index,
                    key: senderList.concat([sender]),
                });
            }
        },

        changeCondition(condition) {
            this.condition = condition;
            this.$store.commit("examList/updateExamObj", {
                cid: this.cid,
                keys: condition,
            });
            this.parseExamListToGroup();
        },
        clickGallery(event, file, type, exam) {
            let list = [...this.extendIworksSameNodeImage(exam.iworksImages), ...(exam.noneIworksImages || [])];
            this.$store.commit("gallery/setGallery", {
                list: list,
                openFile: file,
                loadMore: false,
                loadMoreCallback: async () => {},
            });
            this.$router.push(this.$route.fullPath + "/gallery");
        },
        showProtocol(item) {
            let protocolInfo = this.gallery.iworks_protocol_list[item.iworks_protocol.attributes.GUID];
            this.iworksProtocolTree = protocolInfo.protocolTree;
            let component = this.$refs["protocol_tree_" + item.exam_id];
            let checked = [];
            for (let image of item.iworksImages) {
                let node = findProtocolViewNode(protocolInfo.protocolTree[0], image.protocol_view_guid);
                if (node) {
                    checked.push(node.id);
                }
            }
            component[0].setCheckedKeys(checked);
        },
        deleteFileToExamList(data) {
            let cid = data.cid;
            let examObj = this.$store.state.examList[cid];
            if (!examObj) {
                return;
            }
            let examList = examObj.list;
            let done = false;
            for (let exam of examList) {
                for (let index = 0; index < exam.imageList.length; index++) {
                    if (exam.imageList[index].resource_id == data.resource_id) {
                        let obj = {};
                        exam.imageList.splice(index, 1);
                        obj.imageList = exam.imageList;
                        this.parseIworksImage(exam);
                        obj.count = exam.count - 1;
                        exam.showImageList = exam.iworksImages.concat(exam.noneIworksImages);
                        this.$store.commit("examList/updateExamById", {
                            cid: cid,
                            exam_id: exam.exam_id,
                            key: obj,
                        });
                        done = true;
                        break;
                    }
                }
                if (done) {
                    break;
                }
            }
        },
        findMulticenter() {
            //查询群是否属于多中心
            this.belongingMulticenter = null;
            this.isNormalMember = true;
            const controller = window.main_screen.conversation_list[this.cid];
            return new Promise((resolve, reject) => {
                controller.getMulticenter({}, (res) => {
                    if (res.data) {
                        this.belongingMulticenter = res.data;
                        if (this.belongingMulticenter) {
                            for (let member of this.belongingMulticenter.memberList) {
                                if (member.uid == this.user.uid) {
                                    this.isNormalMember = false;
                                    break;
                                }
                            }
                        }
                        this.getExamStatus();
                        this.getMulticenterStatus();
                    }
                    resolve(true);
                });
            });
        },
        getExamStatus(examList) {
            if (this.belongingMulticenter) {
                if (!examList) {
                    examList = this.examList;
                }
                let ids = [];
                for (let exam of examList) {
                    //临时数据
                    ids.push(exam.exam_id);
                }
                if (ids.length == 0) {
                    return;
                }
                multiCenterService
                    .getExamStatus({
                        mcID: this.belongingMulticenter.id,
                        examIDList: ids,
                    })
                    .then((res) => {
                        console.log("[exam status]", res.data);
                        this.$store.commit("examList/updateExamStatusList", {
                            cid: this.cid,
                            list: res.data.data || [],
                        });
                    })
                    .catch((err) => {
                        console.log("[exam status error]", err);
                    });
            }
        },
        openSupplyCaseDialog(item, needDisabled = false) {
            if (item.disabled) {
                this.$MessageBox.alert(this.$t('repeat_supply'));
                return;
            }
            if (this.belongingMulticenter.type == this.multicenterConfig.name.hfr_multicenter) {
                this.$refs.supplyCase.openSupplyCase(item, this.belongingMulticenter, needDisabled);
            } else if (this.belongingMulticenter.type == this.multicenterConfig.name.thyroid_multicenter) {
                this.$refs.thyroidSupplyCase.openSupplyCase(item, this.belongingMulticenter, needDisabled);
            } else if (this.belongingMulticenter.type == this.multicenterConfig.name.myocardial_strain_multicenter) {
                this.$refs.myocardialSupplyCase.openSupplyCase(item, this.belongingMulticenter, needDisabled);
            } else if (this.belongingMulticenter.type == this.multicenterConfig.name.generic_multicenter) {
                this.$refs.genericSupplyCase.openSupplyCase(item, this.belongingMulticenter, needDisabled);
            }
        },
        ifNeedAiSelectDialog(item) {
            this.currentSuplyExam = item;
            this.examInfo = this.currentSuplyExam;
            this.$refs.upload.click();
        },
        //提交文件
        checkFileInfoTips(file, fileList, isTips = true) {
            const isLt300M = file.raw.size / 1024 / 1024 <= 300;
            if (isTips) {
                if (!isLt300M) {
                    this.debounceUploadMaxMessage();
                }
            }
            return isLt300M
        },
        checkFileInfo(file, fileList) {
            let type = file.raw.type;
            const isJPG = type === "image/jpeg";
            const isPNG = type === "image/png";
            const isAI = type === "application/postscript" && file.raw.name.substr(-3) === ".ai";
            const isMP4 = type === "video/mp4";
            const isPDF = type === "application/pdf";
            const isDCM = Tool.getFileType(file.name) === "dcm";
            const isJSON = type === "application/json";
            return isJPG || isPNG || isMP4 || isPDF || isDCM || isAI|| isJSON
        },
        async handleFilesSelected() {
            // console.error(fileList)
            const fileList = Object.values(this.$refs.upload.files);
            if (!this.examInfo || !this.examInfo.sender_nickname) {
                this.$message.error(this.$t('supply_exam_image.err_tip.not_exam_sender'));
                return;
            } else {
                let is_sender = false;
                for (let i in this.examInfo.sender_nickname) {
                    var sender = this.examInfo.sender_nickname[i];
                    if (sender.sender_id == this.user.uid) {
                        is_sender = true;
                        break;
                    }
                }
                if (!is_sender) {
                    this.$message.error(this.$t('supply_exam_image.err_tip.not_exam_sender'));
                    return;
                }
            }
            if (!fileList || fileList.length == 0) {
                return;
            }
            this.supply_exam_image_input_file_tag = new Date().valueOf();
            let uploadFiles = []
            let isIllegal =false
            fileList.forEach((file, index) => {
                const new_file = {
                    uid:new Date().getTime()+'_'+index,
                    name: file.name,
                    size: file.size,
                    type: file.type,
                    raw: file
                }
                const isLimitPass = this.checkFileInfoTips(new_file,fileList);
                const isCheckFileInfoPass = this.checkFileInfo(new_file,fileList)
                if(isLimitPass && isCheckFileInfoPass ){
                    uploadFiles.push(new_file)
                }else{
                    isIllegal = true
                }

            });
            this.$refs.upload.value = null;
            if(isIllegal){
                this.$message.error(this.$t('upload_forbidden_file_type_text'));
                return
            }
            // 判断上传文件列表是否全为json类型
            // const isAllJson = uploadFiles.every(item => Tool.getFileType(item.name) === 'json');

            // if (isAllJson) {
            //     this.$message.error(this.$t('transmit_less_tip'));
            //     return false;
            // }

            if(uploadFiles.length < 1){
                this.$message.error(this.$t('transmit_less_tip'));
                return false;
            }
            if (uploadFiles.length > 500) {
                this.$message.error(this.$t('supply_exam_image.err_tip.select_up_to_500_files'));
                return false
            }
            this.supplying = true;

            await this.uploadSubmit(uploadFiles)
        },
        //创建批量任务
        async uploadSubmit(uploadFiles) {
            if (!this.examInfo) {
                return;
            }
            if (Object.keys(this.uploadTask).length === 0) {
                let uploadFns = [];
                uploadFiles.forEach((item) => {
                    this.$set(this.uploadTask, item.uid, {
                        file: item,
                        isUploaded: false,
                        percentage: 0,
                        progressShow: false,
                        ossImageUrl: "",
                        isError: false,
                    });

                    uploadFns.push(async () => {
                        return await this.uploadFiles(item.uid);
                    });
                });
                const taskGroups = [];
                for (let i = 0; i < uploadFns.length; i += 2) {
                    taskGroups.push(uploadFns.slice(i, i + 2));
                }

                async function runTaskGroup(taskGroup) {
                    return await Promise.all(taskGroup.map((task) => task()));
                }

                async function runTasks() {
                    let resultArr = [];
                    for (const taskGroup of taskGroups) {
                        const result = await runTaskGroup(taskGroup);
                        resultArr.push(result);
                    }
                    return resultArr;
                }
                runTasks()
                    .then((result) => {
                        console.log("All tasks have been completed.", result);
                        const flatArr = result.flat();
                        this.alertUploadResult(flatArr);
                        this.clearUploadTask();
                        return flatArr;
                    })
                    .catch((error) => {
                        if (error !== "cancel") {
                            this.$MessageBox
                                .alert(this.$t('supply_exam_image.err_tip.upload_file_failed'))
                                .then((action) => {
                                    this.visible = false;
                                });
                        };
                        this.clearUploadTask();

                    })
            } else {
                this.clearUploadTask();
                return [];
            }
        },
        importExamMeasureFiles({ file_name, exam_id }) {
            return new Promise((resolve, reject) => {
                window.main_screen.importExamMeasureFiles(
                    {
                        file_name,
                        exam_id,
                    },
                    (res) => {
                        if (res.error_code === 0) {
                            resolve(true);
                        } else {
                            console.error(res);
                            reject(false);
                        }
                    }
                );
            });
        },
        getItemStatusText(status) {
            return getItemStatusText(status);
        },
        openNewExamModule() {
            this.isShowNewExamPage = true;
        },
        async openIworkTestSummary(exam) {
            await this.loadImageList(exam);
            this.currentExam = exam;
            this.isShowiworksTestExamReportDialog = true;
        },
        handleDeleteExam(exam) {
            this.$MessageBox
                .confirm(this.$t('delete_exam_tips'), this.$t('tip_title'), {
                    confirmButtonText: this.$t('confirm_txt'),
                    cancelButtonText: this.$t('cancel_btn'),
                    closeOnClickModal: false,
                })
                .then(() => {
                    if (!exam.hasOwnProperty("group_id")) {
                        exam.group_id = this.cid;
                    }
                    this.deleteExam(exam);
                })
                .catch((e) => {});
        },
        exportExam(exam) {
            if (!Tool.checkAppClient("Cef")) {
                this.$message.error(this.$t('use_app_tip'));
                return;
            }
            let list = exam.imageList;
            let downLoadUrlList = [];
            list.forEach((item) => {
                let url = this.getDownloadUrl(item);
                if (!url) {
                    console.error(url, item);
                } else {
                    let surl = window.location.origin;
                    if (url.indexOf("http") != 0) {
                        url = surl + "/" + url;
                    }
                    console.log(exam, "exam");
                    const destName = this.formatDestName(exam);
                    downLoadUrlList.push({ src: url, dest_name: destName, msg_type: item.msg_type });
                }
            });
            this.$root.eventBus.$emit("createDownLoadTask", {
                downLoadUrlList,
                needsInputFileName: false,
            });
            this.cancelDownLoadSelected();
        },
        formatDestName(exam) {
            let destName = "";
            if (exam.sender_nickname[0].hospital_name) {
                destName += exam.sender_nickname[0].hospital_name + "-";
            }
            destName += exam.sender_nickname[0].nickname;
            destName += `_${moment(exam.upload_ts).format("YYYY-MM-DD z")}`;
            destName += `_${exam.exam_id}`;
            return destName;
        },
        async openDownLoadExamModule() {
            // if(!Tool.checkAppClient('Cef')){
            //     this.$message.error(this.$t('use_app_tip'))
            //     return
            // }
            if (!this.isCef) {
                this.$message.info(this.$t('client_only_tip'));
                return;
            }
            if (!this.isShowExportCheckbox) {
                this.isShowExportCheckbox = true;
                return;
            }
            if (this.checkList.length === 0) {
                this.$message.error(this.$t('export_empty_tip'));
                return;
            }

            let loadImageListPromise = [];
            const getDownLoadInfo = (exam) => {
                return new Promise(async (resolve, reject) => {
                    let downLoadUrlList = [];
                    console.error(exam.imageList,'exam.imageList')
                    if (!exam.imageList || exam.imageList.length === 0) {
                        try {
                            const res = await this.loadImageList(exam);
                        } catch (error) {
                            console.error(error);
                            resolve(error);
                        }
                    }
                    exam.imageList &&
                        exam.imageList.forEach((item) => {
                            let url = this.getDownloadUrl(item);
                            if (!url) {
                                console.error(url, item);
                            } else {
                                let surl = window.location.origin;
                                if (url.indexOf("http") != 0) {
                                    url = surl + "/" + url;
                                }
                                const destName = this.formatDestName(exam);
                                downLoadUrlList.push({ src: url, dest_name: destName, msg_type: item.msg_type });
                            }
                        });
                    resolve(downLoadUrlList);
                });
            };
            this.checkList.forEach((exam_id) => {
                this.examGroup.forEach((group) => {
                    group.list.forEach((exam) => {
                        if (exam.exam_id === exam_id) {
                            loadImageListPromise.push(async () => {
                                return await getDownLoadInfo(exam);
                            });
                        }
                    });
                });
            });
            this.dataPreparing = true;
            const batchSize = 3;
            const taskGroups = [];
            for (let i = 0; i < loadImageListPromise.length; i += batchSize) {
                taskGroups.push(loadImageListPromise.slice(i, i + batchSize));
            }
            async function runTaskGroup(taskGroup) {
                return await Promise.all(taskGroup.map((task) => task()));
            }

            async function runTasks() {
                let resultArr = [];
                for (const taskGroup of taskGroups) {
                    const data = await runTaskGroup(taskGroup);
                    const result = data.flat();
                    resultArr.push(result);
                    if (result.includes("loadImageList timeout")) {
                        break;
                    }
                }
                return resultArr;
            }
            runTasks()
                .then((result) => {
                    console.log("All tasks have been completed.", result);
                    const flatArr = result.flat();
                    if (flatArr.includes("loadImageList timeout")) {
                        this.$message.error(this.$t('data_loading_failed_tips'));
                        this.dataPreparing = false;
                    } else {
                        this.$root.eventBus.$emit("createDownLoadTask", {
                            downLoadUrlList: flatArr,
                            needsInputFileName: false,
                        });
                        this.dataPreparing = false;
                    }
                })
                .catch((error) => {
                    console.error(error);
                    this.dataPreparing = false;
                });
        },
        handleSelectExam() {
            if (this.checkList.length == this.examListLength) {
                this.isCheckAll = true;
            } else {
                this.isCheckAll = false;
            }
        },
        toggleCheckAll(val) {
            if (val) {
                this.checkList = [];
                this.examGroup.forEach((group) => {
                    group.list.forEach((exam) => {
                        this.checkList.push(exam.exam_id);
                    });
                });
                console.log(this.examGroup);
            } else {
                this.checkList = [];
            }
        },
        cancelDownLoadSelected() {
            this.isShowExportCheckbox = false;
            this.checkList = [];
            this.isCheckAll = false;
        },
        deleteExamItem() {
            this.getMulticenterStatus();
            this.parseExamListToGroup();
        },
        checkDeleteCaseAuth(item) {
            let isSender;
            if (Array.isArray(item.sender_nickname)) {
                item.sender_nickname.forEach((item) => {
                    if (item.sender_id == this.user.uid) {
                        isSender = true;
                    }
                });
            }
            if (item.hasOwnProperty("examStatus") && item.examStatus !== 0) {
                //多中心的病例，非未提交不允许删除
                return false;
            }
            if (this.isCreator || this.isManager || isSender || this.user.uid === item.sender_id) {
                return true;
            }
            return false;
        },
        getImageListLength(oImageList, oCount) {
            if (oImageList.length > 0) {
                let imageList = oImageList.filter((img) => {
                    return getResourceTempState(img.resource_id) === 1;
                });
                return imageList.length;
            } else {
                return oCount;
            }
        },
        getMulticenterStatus() {
            this.multicenterStatusObj = {
                1: 0,
                2: 0,
                3: 0,
                6: 0,
            };
            if (this.isShowMulticenterStatus) {
                multiCenterService
                    .getMultiCenterStatus({
                        mcID: this.belongingMulticenter.id,
                        gid: this.cid,
                    })
                    .then((res) => {
                        if (res.data.error_code === 0) {
                            this.multicenterStatusObj = Object.assign(this.multicenterStatusObj, res.data.data);
                        }
                        console.log("[multiCenter status]", res.data);
                    })
                    .catch((err) => {
                        console.log("[multiCenter status error]", err);
                    });
            }
        },
        refreshMulticenterCount() {
            this.search();
        },
        updateExamInfo(exam_id) {
            return new Promise((resolve, reject) => {
                window.main_screen.updateExamInfo(
                    {
                        exam_id,
                    },
                    (res) => {
                        console.log(res, "updateExamInfo");
                        if (res.error_code === 0) {
                            const { measure_files } = res.data;
                            const obj = { measure_files };
                            this.$store.commit("examList/updateExamInfo", {
                                cid: this.cid,
                                exam_id,
                                obj,
                            });
                            resolve(true);
                        } else {
                            console.error(res);
                            reject(false);
                        }
                    }
                );
            });
        },
        isObstetricExamType(item) {
            //是否为产科
            return item.exam_type == 0;
        },
        updatePatientInfo(exam, msg) {
            if (msg.patient_id) {
                let patientInfo = transferPatientInfo(msg);
                this.$store.commit("examList/updateExamById", {
                    cid: msg.group_id,
                    exam_id: exam.exam_id,
                    key: {
                        patientInfo,
                    },
                });
            }
        },
    },
};
</script>
<style lang="scss">
.exam_mode_page {
    height: 100%;
    display: flex;
    flex-direction: column;
    .multicenter_count {
        display: flex;
        padding: 10px;
        background: #fff;
        border-top: 1px solid #eee;
        & > span {
            margin-right: 20px;
        }
    }
    .exam_right_tools {
        display: flex;
        position: absolute;
        right: 0;
        top: 0;
        justify-content: center;
        align-items: center;
        height: 48px;
        .toolbar_btn {
            font-size: 26px;
            line-height: 28px;
            cursor: pointer;
            color: #999;
            height: 28px;
            margin: 0 10px;
            &.icondownload {
                &.active {
                    color: #01c59d;
                }
            }
        }
    }

    .exam_list {
        flex: 1;
        overflow: hidden;
        padding: 20px;
        display: flex;
        flex-direction: column;
        .select_checkbox {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            width: 100px;
            .el-checkbox__inner {
                width: 20px;
                height: 20px;
                border-radius: 50%;
                &::after {
                    height: 10px;
                    left: 7px;
                    top: 3px;
                }
            }
        }
        .__vuescroll {
            flex: 1;
        }
        .__view {
            width: 100% !important;
        }
        .group_container {
            .exam_item_container {
                display: flex;
                position: relative;
                .select_checkbox {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 20px;
                    height: 20px;
                    transform: translateY(50%);
                }
            }
            .group_title {
                line-height: 1.8;
                margin-top: 10px;
                display: flex;
                align-items: center;
            }
            .exam_item > .exam_title {
                cursor: pointer;
            }
            .exam_title {
                font-size: 0.8rem;
                color: #333;
                line-height: 1.6;
                position: relative;
                .exam_uploader {
                    display: flex;
                    padding-bottom: 0.6rem;
                    border-bottom: 1px solid #dbdbdb;
                    .avatar {
                        width: 2rem;
                        height: 2rem;
                        border-radius: 50%;
                        overflow: hidden;
                        img {
                            width: 100%;
                            height: 100%;
                        }
                    }
                    .uploader_info {
                        flex: 1;
                        padding-left: 0.6rem;
                        .date {
                            font-size: 0.6rem;
                            color: #999;
                        }
                    }
                }
                .iworks_info {
                    display: flex;
                    align-items: center;
                    .icon_title {
                        background-color: #666;
                        border-radius: 50%;
                        width: 1rem;
                        height: 1rem;
                        i {
                            position: relative;
                            font-size: 0.9em;
                            top: -0.1em;
                            left: 0.225em;
                            fill: none;
                            color: #fff;
                            float: left;
                        }
                    }
                    & > p {
                        display: inline-block;
                        line-height: 1rem;
                        padding-left: 0.4rem;
                        font-size: 0.55rem;
                    }
                }
                .icondown,
                .iconup {
                    position: absolute;
                    right: 0;
                    top: 22px;
                    line-height: 1;
                    font-size: 26px;
                }
                .iconcomment {
                    position: absolute;
                    right: 2px;
                    top: -6px;
                    font-size: 22px;
                    span {
                        position: absolute;
                        right: -6px;
                        top: 0px;
                        min-width: 16px;
                        height: 16px;
                        line-height: 16px;
                        border-radius: 50%;
                        background-color: #0dc6a0;
                        text-align: center;
                        color: #ffffff;
                        font-size: 12px;
                    }
                }
                .iconwarning-o {
                    cursor: pointer;
                }
                .iWorks_test {
                    padding: 2px 5px 2px 5px;
                    color: white;
                    background: rgba(120, 201, 87, 1);
                    border-radius: 5px;
                    position: relative;
                    float: right;
                    right: 30px;
                    font-size: 13px;
                }
            }
            .exam_item {
                flex: 1;
                padding: 0.8rem 0.8rem 1rem;
                margin-top: 14px;
                background-color: #fff;
                border-radius: 0.3rem;
                box-shadow: 0.1rem 0.1rem 0.2rem rgba(140, 152, 155, 0.7);
                min-width: 0;
                .exam_patient {
                    font-size: 0.7rem;
                    padding-top: 0.6rem;
                    color: rgb(101, 109, 112);
                    display: flex;
                    display: flex;
                    justify-content: space-between;
                    flex-wrap: wrap;
                    .field {
                        margin-top: 10px;
                        display: block;
                        width: 33%;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                    }
                    .refute_reason {
                        color: red;
                    }
                    .patient_info {
                        display: flex;

                        margin-bottom: 0.5rem;
                        p {
                            flex: 1;
                            word-break: break-all;
                            padding-right: 0.3rem;
                        }
                        .iworks_test_summary_parent {
                            width: calc(100% - 200px);
                        }
                        .detail_button {
                            color: blue;
                        }
                        .iworks_test_summary {
                            color: #569299 !important;
                        }
                        .standard_number {
                            color: blue;
                        }
                        .hover_tips {
                            position: relative;
                            bottom: 5px;
                            margin-left: 5px;
                            font-size: 13px;
                        }
                        .click_tips {
                            margin-left: 5px;
                            font-size: 13px;
                            color: rgb(255, 153, 51);
                        }
                    }
                }
                .exam_image_list {
                    background-color: #fff;
                    .list {
                        margin-top: 10px;
                    }
                    .file_item {
                        float: left;
                        margin-right: 6px;
                        margin-bottom: 6px;
                        width: 140px;
                        height: 110px;
                        background-color: #000;
                        position: relative;
                        cursor: pointer;
                        border-radius: 0.2rem;
                        img {
                            max-width: 100%;
                            max-height: 100%;
                            position: absolute;
                            top: 50%;
                            left: 50%;
                            transform: translate(-50%, -50%);
                        }
                        i {
                            position: absolute;
                            bottom: 6px;
                            color: #fff;
                            font-size: 24px;
                            line-height: 1;
                            left: 4px;
                        }
                        .review_time {
                            font-size: 12px;
                            text-align: center;
                            color: #fff;
                            position: absolute;
                            white-space: nowrap;
                            top: 50%;
                            left: 50%;
                            transform: translate(-50%, -50%);
                        }
                        .review_text {
                            font-size: 12px;
                            text-align: center;
                            color: yellow;
                            position: absolute;
                            white-space: nowrap;
                            width: 100%;
                            top: 5%;
                        }
                        &.is_create {
                            .view_name {
                                color: #f9f122;
                            }
                        }
                        &.is_delete {
                            .view_name {
                                text-decoration: line-through;
                            }
                        }
                        .view_name {
                            color: #fff;
                            position: absolute;
                            top: 6px;
                            z-index: 9;
                            left: 2px;
                            transform: none;
                            font-size: 14px;
                            white-space: normal;
                            text-align: left;
                        }
                        .view_count {
                            color: #fff;
                            position: absolute;
                            bottom: 6px;
                            z-index: 9;
                            right: 2px;
                            font-size: 12px;
                        }
                        .empty_view {
                            width: 100%;
                            height: 100px;
                            position: absolute;
                            top: 50%;
                            left: 50%;
                            background-color: #2c2d2f;
                            transform: translate(-50%, -50%);
                        }
                        .iconplus {
                            width: 24px;
                            height: 24px;
                            position: absolute;
                            top: 50%;
                            left: 50%;
                            transform: translate(-50%, -50%);
                        }
                        .qc_standard_icon {
                            right: 3px;
                            bottom: 0px;
                            position: absolute;
                            color: rgb(0, 255, 102);
                            font-size: 10px;
                            z-index: 10;
                        }
                        .bg_blue {
                            background: rgb(0, 255, 102);
                        }
                        .bg_red {
                            background: rgb(255, 0, 0);
                        }
                        .dr_result_icon {
                            right: 3px;
                            bottom: 3px;
                            position: absolute;
                            color: rgb(241 246 243);
                            font-size: 12px;
                            height: 17px;
                            width: 16px;
                            border-radius: 7px;
                            display: block;
                            text-align: center;
                            line-height: 19px;
                            font-weight: bold;
                        }
                        .qc_non_standard_icon {
                            right: 3px;
                            bottom: 0px;
                            position: absolute;
                            color: rgb(255, 153, 51);
                            font-size: 10px;
                            z-index: 10;
                        }
                        .ai_result_deletion_icon {
                            right: 3px;
                            bottom: 0px;
                            position: absolute;
                            color: rgb(255, 0, 0);
                            font-size: 10px;
                            z-index: 10;
                        }
                    }
                    .iworks_panel {
                        border: 1px solid #aaa;
                        margin-bottom: 10px;
                        padding: 10px;
                        border-radius: 4px;
                        & > p {
                            margin-bottom: 4px;
                            padding-right: 26px;
                            position: relative;
                            display: inline-block;
                            i {
                                font-size: 20px;
                                position: absolute;
                                right: 0;
                                top: -2px;
                                cursor: pointer;
                            }
                        }
                        .iworks_list {
                            .iworks_view {
                                float: left;
                                margin-right: 6px;
                                margin-bottom: 6px;
                                width: 140px;
                                height: 120px;
                                background-color: #000;
                                position: relative;
                                cursor: pointer;
                                img {
                                    max-width: 100%;
                                    max-height: 100%;
                                    position: absolute;
                                    top: 50%;
                                    left: 50%;
                                    transform: translate(-50%, -50%);
                                }
                                & > p {
                                    color: #f9f90f;
                                    position: absolute;
                                    top: 6px;
                                    z-index: 9;
                                    left: 2px;
                                }
                            }
                        }
                    }
                }
                .exam_image_tool {
                    display: flex;
                }
                .exam_comments {
                    border-top: 1px solid #aaa;
                    margin-top: 10px;
                    padding-top: 10px;
                    .comment_editor {
                        display: flex;
                        border-bottom: 1px solid #aaa;
                        padding-bottom: 10px;
                        & > p {
                            font-size: 18px;
                            line-height: 50px;
                            margin-right: 8px;
                        }
                        textarea {
                            resize: none;
                            font-size: 16px;
                            font-weight: normal;
                            flex: 1;
                            line-height: 18px;
                            color: #555555;
                            border: 1px solid #ccc;
                            padding: 4px;
                            border-radius: 3px;
                        }
                        .add_exam_comment {
                            background: #a1b7b6;
                            margin: 8px;
                            padding: 2px 10px;
                            color: #fff;
                            border-radius: 3px;
                            line-height: 32px;
                            cursor: pointer;
                        }
                    }
                    .exam_comment_list {
                        padding: 12px 0px;
                        .comment_item {
                            margin: 4px;
                            color: #333;
                            padding: 8px 4px;
                            border-bottom: 1px solid #efefef;
                            .author_info,
                            .author_info_time {
                                color: #666;
                                .author {
                                    margin-right: 0.3rem;
                                    line-height: 1rem;
                                }
                            }
                            & > p {
                                font-size: 16px;
                                user-select: text;
                                span {
                                    font-size: 12px;
                                    background-color: #01c59d;
                                    color: #fff;
                                    line-height: 1;
                                    padding: 4px 6px;
                                    border-radius: 4px;
                                    margin-top: 0;
                                    margin-left: 4px;
                                    display: inline-block;
                                }
                            }
                            .author_info_time {
                                text-align: right;
                            }
                        }
                    }
                }
            }
        }
        .tip {
            cursor: pointer;
            text-align: center;
            line-height: 2;
            margin-top: 8px;
        }
        .supply_exam_image_input_file {
            display: none;
        }
    }
}
.supplementary_image_types {
    .el-message-box__btns {
        text-align: center;
        button {
            color: #fff;
            background: #779a98;
            border-color: #779a98;
        }
    }
}

.exam_mode_search_tags {
    .choose_tags_wrap {
        width: 340px;
        .add_custom_tag {
            display: flex;
            .el-input {
                margin-right: 8px;
                input {
                    height: 30px;
                }
            }
        }
        .tag_names {
            & > div {
                color: #fff;
                background: #83a5a1;
                padding: 0 8px;
                border-radius: 8px;
                margin: 4px;
                cursor: pointer;
                position: relative;
                font-size: 16px;
                line-height: 1.6;
                float: left;
                &.selected {
                    background: #ccc;
                }
            }
        }
    }
}
</style>
